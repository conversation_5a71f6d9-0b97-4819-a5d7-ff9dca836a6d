# Complete API Flow Analysis: MAUI to Web App

## Overview
This document provides a comprehensive breakdown of all API calls in the Dr. Muscle MAUI app user flow, showing endpoints, request/response structures, and local handling logic.

## 1. Login Flow

### 1.1 Login API Call
**Endpoint**: `POST /token`  
**Base URL**: `https://drmuscle.azurewebsites.net/`

```csharp
// DrMuscleRestClient.cs
public async Task<LoginSuccessResult> Login(LoginModel model)
{
    HttpResponseMessage response;
    LoginSuccessResult token = null;
    using (var client = new HttpClient())
    {
        client.BaseAddress = new Uri(BaseUrl);
        var body = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("grant_type","password"),
            new KeyValuePair<string, string>("password",model.Password),
            new KeyValuePair<string, string>("username",model.Username)
        };
        var content = new FormUrlEncodedContent(body);
        response = await client.PostAsync("token", content);
        
        string raw = await response.Content.ReadAsStringAsync();
        token = JsonConvert.DeserializeObject<LoginSuccessResult>(raw);
        SetToken(token.access_token);
    }
    return token;
}
```

**Request Model**:
```csharp
public class LoginModel
{
    public string Username { get; set; } // Email address
    public string Password { get; set; }
}
```

**Response Model**:
```csharp
public class LoginSuccessResult
{
    public string access_token { get; set; }
    public string token_type { get; set; }
    public int expires_in { get; set; }
    public string userName { get; set; }
    public string email { get; set; }
}
```

### 1.2 Get User Info (Post-Login)
**Endpoint**: `GET /api/Account/GetUserInfo`

```csharp
// DrMuscleRestClient.cs
public async Task<UserInfosModel> GetUserInfo()
{
    UserInfosModel model = await GetTaskAsyncNew<UserInfosModel>("api/Account/GetUserInfo");
    return model;
}
```

**Response Model**: Contains ~60+ user settings including:
- Personal info (name, email, gender, age)
- Body measurements (weight, height, mass unit)
- Workout preferences (rep ranges, set styles, equipment)
- App settings (timers, reminders, quick mode)

### 1.3 Local Storage After Login
```csharp
// WelcomePage.xaml.cs
LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
LocalDBManager.Instance.SetDBSetting("token_expires_date", 
    DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
LocalDBManager.Instance.SetDBSetting("email", uim.Email);
LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
// ... stores all user settings locally
```

## 2. Start Workout Flow

### 2.1 Get User Program Info
**Endpoint**: `GET /api/Account/GetUserProgramInfo`

```csharp
// MainAIPage.xaml.cs
public async void StartTodaysWorkout()
{
    var userProgram = await DrMuscleRestClient.Instance.GetUserProgramInfo();
    
    // Process the next workout
    if (userProgram != null && userProgram.NextWorkoutTemplate != null)
    {
        CurrentLog.Instance.CurrentWorkoutTemplate = 
            userProgram.NextWorkoutTemplate;
        CurrentLog.Instance.WorkoutTemplateCurrentExercise = 
            userProgram.NextWorkoutTemplate.Exercises.First();
            
        await PagesFactory.PushAsync<KenkoDemoWorkoutExercisePage>();
    }
}
```

**Response Model**:
```csharp
public class GetUserProgramInfoResponseModel
{
    public GetUserWorkoutTemplateGroupModel UserWorkoutTemplateGroup { get; set; }
    public GetUserWorkoutTemplateModel NextWorkoutTemplate { get; set; }
    public int? WorkoutId { get; set; }
    public bool IsNewUser { get; set; }
    public int RemainingWorkouts { get; set; }
    public string NextWorkoutLabel { get; set; }
}
```

### 2.2 Load Exercises for Workout
Exercises are loaded from the workout template:
```csharp
// KenkoDemoWorkoutExercisePage.xaml.cs
var exercises = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises;
foreach (var exercise in exercises)
{
    // Check if exercise is completed locally
    var isCompleted = LocalDBManager.Instance.GetDBSetting(
        $"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exercise.Id}_completed") == "true";
}
```

## 3. Open Exercise Flow

### 3.1 Check if New Exercise
**Endpoint**: `POST /api/Exercise/IsNewExerciseWithSessionInfo`

```csharp
// KenkoDemoWorkoutExercisePage.xaml.cs
public async Task<bool> CheckIfNewExercise(long exerciseId)
{
    IsNewExerciseModel request = new IsNewExerciseModel()
    {
        ExerciseId = exerciseId,
        Username = LocalDBManager.Instance.GetDBSetting("email")?.Replace(" ", "").ToLower()
    };
    
    var isNew = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(request);
    return isNew;
}
```

### 3.2 Get Exercise Recommendations
**Endpoint**: `POST /api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew` (for normal sets)  
**Endpoint**: `POST /api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew` (for rest-pause)

```csharp
// KenkoDemoWorkoutExercisePage.xaml.cs
private async Task FetchNextExerciseBackgroundData(ExerciseWorkSetsModel model)
{
    GetRecommendationForExerciseModel request = new GetRecommendationForExerciseModel()
    {
        Username = LocalDBManager.Instance.GetDBSetting("email").Replace(" ", "").ToLower(),
        ExerciseId = model.Id,
        IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode") == "true",
        LightSessionDays = 9,
        WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id
    };

    // Choose endpoint based on set style
    if (LocalDBManager.Instance.GetDBSetting("SetStyle") == "RestPause")
    {
        recommendation = await DrMuscleRestClient.Instance
            .GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew(request);
    }
    else
    {
        recommendation = await DrMuscleRestClient.Instance
            .GetRecommendationNormalRIRForExerciseWithoutWarmupsNew(request);
    }
}
```

**Response Model**:
```csharp
public class RecommendationModel
{
    public MultiUnityWeight Weight { get; set; }
    public int Reps { get; set; }
    public List<WarmupModel> WarmUpSets { get; set; }
    public int Series { get; set; }
    public bool IsLightSession { get; set; }
    public bool IsSystemExercise { get; set; }
    public string RecommendationInKg { get; set; }
    public string RecommendationInLb { get; set; }
}
```

## 4. Save Set Flow

### 4.1 Add Individual Set
**Endpoint**: `POST /api/Exercise/AddWorkoutLogSerieNew`

```csharp
// SaveSetPage.xaml.cs
private async Task SaveSet(WorkoutLogSerieModelEx set)
{
    WorkoutLogSerieModel setData = new WorkoutLogSerieModel()
    {
        ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id,
        Username = LocalDBManager.Instance.GetDBSetting("email").Replace(" ", "").ToLower(),
        Weight = set.Weight,
        Reps = set.Reps,
        RIR = set.RIR,
        IsWarmups = set.IsWarmups,
        WorkoutId = CurrentLog.Instance.WorkoutId,
        Id = set.Id,
        OneRM = set.OneRM,
        BodyWeight = set.BodyWeight,
        Increments = set.Increments
    };
    
    await DrMuscleRestClient.Instance.AddWorkoutLogSerieNew(setData);
}
```

### 4.2 Save Multiple Sets (Batch)
**Endpoint**: `POST /api/Exercise/AddWorkoutLogSerieListNew`

```csharp
// EndExercisePage.xaml.cs
private async Task SaveAllSets()
{
    List<WorkoutLogSerieModel> setList = new List<WorkoutLogSerieModel>();
    
    foreach (var set in CurrentLog.Instance.ExerciseLog.Series)
    {
        setList.Add(new WorkoutLogSerieModel()
        {
            ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id,
            Username = LocalDBManager.Instance.GetDBSetting("email").Replace(" ", "").ToLower(),
            Weight = set.Weight,
            Reps = set.Reps,
            RIR = set.RIR,
            IsWarmups = set.IsWarmups,
            WorkoutId = CurrentLog.Instance.WorkoutId
        });
    }
    
    await DrMuscleRestClient.Instance.AddWorkoutLogSerieListNew(setList);
}
```

### 4.3 Update Light Session Info
**Endpoint**: `POST /api/Exercise/AddUpdateExerciseUserLightSession`

```csharp
private async Task UpdateLightSessionInfo(long exerciseId, bool isLightSession)
{
    ExerciseUpdateLightSessionModel model = new ExerciseUpdateLightSessionModel()
    {
        Username = LocalDBManager.Instance.GetDBSetting("email").Replace(" ", "").ToLower(),
        ExerciseId = exerciseId,
        IsLightSession = isLightSession
    };
    
    await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSession(model);
}
```

## 5. Save Exercise Flow

### 5.1 Mark Exercise Complete Locally
```csharp
// EndExercisePage.xaml.cs
private void MarkExerciseComplete()
{
    // Mark exercise as completed in local storage
    LocalDBManager.Instance.SetDBSetting(
        $"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{CurrentLog.Instance.ExerciseLog.Exercice.Id}_completed", 
        "true");
    
    // Update current exercise index
    var currentIndex = exercises.IndexOf(CurrentLog.Instance.WorkoutTemplateCurrentExercise);
    LocalDBManager.Instance.SetDBSetting(
        $"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}_exercise_index", 
        currentIndex.ToString());
}
```

### 5.2 Navigate to Next Exercise
```csharp
private async Task NavigateToNextExercise()
{
    var exercises = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises;
    var currentIndex = exercises.IndexOf(CurrentLog.Instance.WorkoutTemplateCurrentExercise);
    
    if (currentIndex < exercises.Count - 1)
    {
        // Move to next exercise
        CurrentLog.Instance.WorkoutTemplateCurrentExercise = exercises[currentIndex + 1];
        await PagesFactory.PushAsync<KenkoDemoWorkoutExercisePage>();
    }
    else
    {
        // All exercises complete - save workout
        await SaveWorkout();
    }
}
```

## 6. Save Workout Flow

### 6.1 Save Complete Workout
**Endpoint**: `POST /api/Workout/SaveGetWorkoutInfo` or `/api/Workout/SaveGetWorkoutInfoPro`

```csharp
// MainAIPage.xaml.cs
private async Task<SaveWorkoutResponseModel> SavingExcercise()
{
    SaveWorkoutModel request = new SaveWorkoutModel()
    {
        WorkoutId = Convert.ToInt32(CurrentLog.Instance.CurrentWorkoutTemplate.Id),
        WorkoutTemplateId = Convert.ToInt32(CurrentLog.Instance.CurrentWorkoutTemplate.Id)
    };
    
    var response = await DrMuscleRestClient.Instance.SaveGetWorkoutInfo(request);
    
    // Clear local workout state
    LocalDBManager.Instance.SetDBSetting($"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}_completed", "");
    
    // Reset exercise completion flags
    foreach (var exercise in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
    {
        LocalDBManager.Instance.SetDBSetting(
            $"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{exercise.Id}_completed", "");
    }
    
    return response;
}
```

**Response Model**:
```csharp
public class SaveWorkoutResponseModel
{
    public GetUserProgramInfoResponseModel UserProgramInfo { get; set; }
    public string NextWorkoutDays { get; set; }
    public bool IsWorkoutCompleted { get; set; }
    public int ConsecutiveWorkouts { get; set; }
    public WorkoutStatsModel WorkoutStats { get; set; }
}
```

## Error Handling Pattern

All API calls follow this error handling pattern:

```csharp
try
{
    UserDialogs.Instance.ShowLoading("Loading...");
    var result = await DrMuscleRestClient.Instance.ApiCall(parameters);
    
    if (result != null)
    {
        // Process successful result
    }
    else
    {
        await UserDialogs.Instance.AlertAsync(new AlertConfig()
        {
            Title = AppResources.Error,
            Message = AppResources.PleaseCheckInternetConnection,
            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        });
    }
}
catch (Exception ex)
{
    await UserDialogs.Instance.AlertAsync(new AlertConfig()
    {
        Title = AppResources.Error,
        Message = AppResources.SomethingWentWrong,
        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
    });
}
finally
{
    UserDialogs.Instance.HideLoading();
}
```

## Authentication Headers

All authenticated API calls include bearer token:

```csharp
// DrMuscleRestClient.cs
private void ConfigureHttpClient(HttpClient client)
{
    if (!string.IsNullOrEmpty(_token))
    {
        client.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("bearer", _token);
    }
}
```

## Key Local Storage Items

The app maintains these key items in local storage:

1. **Authentication**: `token`, `token_expires_date`, `email`
2. **User Info**: `firstname`, `lastname`, `gender`, `age`, `massunit`
3. **Workout State**: `workout{id}_completed`, `workout{id}_exercise_index`
4. **Exercise State**: `workout{id}exercise{id}_completed`
5. **Settings**: `QuickMode`, `SetStyle`, `timer_vibrate`, `timer_sound`

## Summary of API Flow

1. **Login** → Get token → Fetch user info → Store locally
2. **Start Workout** → Get program info → Load workout template → Navigate to first exercise
3. **Open Exercise** → Check if new → Get recommendations → Display to user
4. **Save Sets** → Log each set → Update RIR → Save to server
5. **Complete Exercise** → Mark complete locally → Navigate to next
6. **Save Workout** → Send workout complete → Get next workout info → Update UI