import { test, expect } from '@playwright/test'

test.describe('Reps Increment/Decrement Buttons', () => {
  test('should increment and decrement reps using buttons', async ({
    page,
  }) => {
    // Login
    await page.goto('/login')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('Dr123456')
    await page.getByRole('button', { name: /login/i }).click()
    await page.waitForURL('/workout', { timeout: 10000 })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Find the reps input
    const repsInput = page.locator('#reps-input')

    // Get initial value
    const initialValue = await repsInput.inputValue()
    const initialReps = parseInt(initialValue, 10)

    // Test increment button
    const incrementButton = page.getByRole('button', { name: 'Increase reps' })
    await incrementButton.click()

    // Verify reps increased by 1
    const incrementedValue = await repsInput.inputValue()
    expect(parseInt(incrementedValue, 10)).toBe(initialReps + 1)

    // Test decrement button
    const decrementButton = page.getByRole('button', { name: 'Decrease reps' })
    await decrementButton.click()
    await decrementButton.click()

    // Verify reps decreased by 2 (back to initial - 1)
    const decrementedValue = await repsInput.inputValue()
    expect(parseInt(decrementedValue, 10)).toBe(initialReps - 1)

    // Test minimum boundary (should not go below 1)
    // Click many times to ensure we hit the minimum
    const clicksNeeded = initialReps + 5
    // Perform multiple clicks sequentially
    await decrementButton.click({ clickCount: clicksNeeded })

    const minValue = await repsInput.inputValue()
    expect(parseInt(minValue, 10)).toBe(1)

    // Test that increment still works from minimum
    await incrementButton.click()
    const afterMinIncrement = await repsInput.inputValue()
    expect(parseInt(afterMinIncrement, 10)).toBe(2)
  })

  test('should have proper mobile touch targets', async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('Dr123456')
    await page.getByRole('button', { name: /login/i }).click()
    await page.waitForURL('/workout', { timeout: 10000 })
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await startButton.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Check increment button size
    const incrementButton = page.getByRole('button', { name: 'Increase reps' })
    const incrementBox = await incrementButton.boundingBox()
    expect(incrementBox).not.toBeNull()
    expect(incrementBox!.width).toBeGreaterThanOrEqual(44)
    expect(incrementBox!.height).toBeGreaterThanOrEqual(44)

    // Check decrement button size
    const decrementButton = page.getByRole('button', { name: 'Decrease reps' })
    const decrementBox = await decrementButton.boundingBox()
    expect(decrementBox).not.toBeNull()
    expect(decrementBox!.width).toBeGreaterThanOrEqual(44)
    expect(decrementBox!.height).toBeGreaterThanOrEqual(44)
  })
})
