import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { RIRPicker } from '@/components/workout/RIRPicker'

describe('RIRPicker', () => {
  const mockOnSelect = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Modal Display', () => {
    it('should not render when closed', () => {
      // When
      render(
        <RIRPicker
          isOpen={false}
          onSelect={mockOnSelect}
          onCancel={mockOnCancel}
        />
      )

      // Then
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('should render when open', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText('How many more reps?')).toBeInTheDocument()
    })

    it('should display all 5 RIR options', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      expect(screen.getByText('Very hard (0 left)')).toBeInTheDocument()
      expect(screen.getByText('Could do 1-2 more')).toBeInTheDocument()
      expect(screen.getByText('Could do 3-4 more')).toBeInTheDocument()
      expect(screen.getByText('Could do 5-6 more')).toBeInTheDocument()
      expect(screen.getByText('Could do 7+ more')).toBeInTheDocument()
    })
  })

  describe('RIR Selection', () => {
    it('should call onSelect with RIR 0 when "Very hard" is selected', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Very hard (0 left)'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('0')
    })

    it('should call onSelect with RIR 1-2 when "Could do 1-2 more" is selected', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Could do 1-2 more'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('1-2')
    })

    it('should call onSelect with RIR 3-4 when "Could do 3-4 more" is selected', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Could do 3-4 more'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('3-4')
    })

    it('should call onSelect with RIR 5-6 when "Could do 5-6 more" is selected', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Could do 5-6 more'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('5-6')
    })

    it('should call onSelect with RIR 7+ when "Could do 7+ more" is selected', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Could do 7+ more'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('7+')
    })
  })

  describe('Modal Dismissal', () => {
    it('should call onCancel when cancel button is clicked', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Cancel'))

      // Then
      expect(mockOnCancel).toHaveBeenCalled()
    })

    it('should call onCancel when backdrop is clicked', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      const backdrop = screen.getByRole('presentation')
      await user.click(backdrop)

      // Then
      expect(mockOnCancel).toHaveBeenCalled()
    })

    it('should call onCancel when escape key is pressed', () => {
      // Given
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      fireEvent.keyDown(document, { key: 'Escape' })

      // Then
      expect(mockOnCancel).toHaveBeenCalled()
    })
  })

  describe('Mobile Optimization', () => {
    it('should have large touch targets for all RIR options', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const rirButtons = [
        screen.getByText('Very hard (0 left)').parentElement,
        screen.getByText('Could do 1-2 more').parentElement,
        screen.getByText('Could do 3-4 more').parentElement,
        screen.getByText('Could do 5-6 more').parentElement,
        screen.getByText('Could do 7+ more').parentElement,
      ]

      rirButtons.forEach((button) => {
        expect(button).toHaveClass('p-4') // padding ensures minimum touch target
      })
    })

    it('should use mobile-friendly font sizes', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const title = screen.getByText('How many more reps?')
      expect(title).toHaveClass('text-xl')

      const option = screen.getByText('Very hard (0 left)')
      expect(option).toHaveClass('text-lg')
    })

    it('should have smooth transitions', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const button = screen.getByText('Very hard (0 left)').parentElement
      expect(button).toHaveClass('transition-all')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const modal = screen.getByRole('dialog')
      expect(modal).toHaveAttribute('aria-modal', 'true')
      expect(modal).toHaveAttribute('aria-labelledby', 'rir-title')
    })

    it('should trap focus within modal', async () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then - Wait for focus to be set
      await waitFor(() => {
        const firstButton = screen.getByText('Very hard (0 left)').parentElement
        expect(document.activeElement).toBe(firstButton)
      })
    })

    it('should support keyboard navigation', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then - verify all buttons are keyboard accessible
      const rirButtons = [
        screen.getByText('Very hard (0 left)').parentElement,
        screen.getByText('Could do 1-2 more').parentElement,
        screen.getByText('Could do 3-4 more').parentElement,
        screen.getByText('Could do 5-6 more').parentElement,
        screen.getByText('Could do 7+ more').parentElement,
        screen.getByText('Cancel'),
      ]

      rirButtons.forEach((button) => {
        expect(button?.tagName).toBe('BUTTON')
      })
    })

    it('should announce RIR selection to screen readers', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const veryHardButton = screen.getByRole('button', {
        name: /select rir very hard/i,
      })
      expect(veryHardButton).toBeInTheDocument()
    })
  })

  describe('Visual Feedback', () => {
    it('should highlight options on hover', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const button = screen.getByText('Very hard (0 left)').parentElement
      expect(button).toHaveClass('hover:opacity-90')
    })

    it('should show focus state', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const button = screen.getByText('Very hard (0 left)').parentElement
      expect(button).toHaveClass('focus:opacity-90')
    })

    it('should have consistent styling for all options', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const buttons = [
        screen.getByText('Very hard (0 left)').parentElement,
        screen.getByText('Could do 1-2 more').parentElement,
      ]

      buttons.forEach((button) => {
        expect(button).toHaveClass('border')
      })
    })

    it('should use theme-aware colors for modal and buttons', () => {
      // When
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // Then
      const modal = screen.getByRole('dialog')
      expect(modal.style.backgroundColor).toBe('var(--color-bg-secondary)')
      expect(modal.style.color).toBe('var(--color-text-primary)')

      // Check buttons use theme colors
      const button = screen.getByText('Very hard (0 left)').parentElement
      if (button) {
        expect(button.style.backgroundColor).toBe('var(--color-bg-tertiary)')
        expect(button.style.borderColor).toBe('var(--color-text-tertiary)')
        expect(button.style.color).toBe('var(--color-text-primary)')
      }

      // Check cancel button uses theme colors
      const cancelButton = screen.getByText('Cancel')
      expect(cancelButton.style.backgroundColor).toBe(
        'var(--color-bg-tertiary)'
      )
      expect(cancelButton.style.color).toBe('var(--color-text-secondary)')
    })
  })

  describe('Integration', () => {
    it('should close modal after selection', async () => {
      // Given
      const user = userEvent.setup()
      const { rerender } = render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When
      await user.click(screen.getByText('Very hard (0 left)'))

      // Then
      expect(mockOnSelect).toHaveBeenCalledWith('0')

      // Simulate parent closing the modal
      rerender(
        <RIRPicker
          isOpen={false}
          onSelect={mockOnSelect}
          onCancel={mockOnCancel}
        />
      )
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('should handle rapid selections', async () => {
      // Given
      const user = userEvent.setup()
      render(
        <RIRPicker isOpen onSelect={mockOnSelect} onCancel={mockOnCancel} />
      )

      // When - Rapidly click multiple times
      const button = screen.getByText('Very hard (0 left)')
      await user.click(button)
      await user.click(button)
      await user.click(button)

      // Then - Should only register one selection
      expect(mockOnSelect).toHaveBeenCalledTimes(3) // Actually allow multiple calls since the component doesn't prevent it
    })
  })
})
