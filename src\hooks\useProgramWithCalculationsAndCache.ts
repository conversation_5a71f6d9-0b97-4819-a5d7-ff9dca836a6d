import { useMemo } from 'react'
import {
  useProgram<PERSON>ith<PERSON>ache,
  useProgramProgressWithCache,
  useProgramStatsWithCache,
} from './useProgramWithCache'
import {
  calculateProgress,
  calculateCompletionDate,
  getDaysRemaining,
  getMissedWorkouts,
  getWorkoutsPerWeek,
} from '@/lib/programCalculations'
import type { ProgramProgress, ProgramStats } from '@/types'

/**
 * Cache-enhanced hook that combines program API data with calculated metrics
 * Shows cached data immediately while fetching fresh data in background
 */
export function useProgramWithCalculationsAndCache() {
  const {
    program,
    isLoading: programLoading,
    isRefreshing: programRefreshing,
    error: programError,
    refetch: refetchProgram,
    hasInitialData: hasInitialProgram,
  } = useProgramWithCache()

  const {
    progress: apiProgress,
    isLoading: progressLoading,
    isRefreshing: progressRefreshing,
    error: progressError,
    refetch: refetchProgress,
    hasInitialData: hasInitialProgress,
  } = useProgramProgressWithCache()

  const {
    stats,
    isLoading: statsLoading,
    isRefreshing: statsRefreshing,
    error: statsError,
    refetch: refetchStats,
    hasInitialData: hasInitialStats,
  } = useProgramStatsWithCache()

  // Calculate enhanced progress using our utilities
  const enhancedProgress = useMemo(() => {
    if (!program) return null

    // Use API progress as base but enhance with calculations
    const calculatedProgress = calculateProgress(program, [])

    // Merge API data with calculated data, preferring API where available
    const progress: ProgramProgress = {
      percentage: apiProgress?.percentage ?? calculatedProgress.percentage,
      daysCompleted: calculatedProgress.daysCompleted,
      currentWeek: calculatedProgress.currentWeek,
      totalWorkouts:
        apiProgress?.totalWorkouts ?? calculatedProgress.totalWorkouts,
      workoutsThisWeek:
        apiProgress?.workoutsThisWeek ?? calculatedProgress.workoutsThisWeek,
      remainingWorkouts:
        apiProgress?.remainingWorkouts ?? calculatedProgress.remainingWorkouts,
    }

    return progress
  }, [program, apiProgress])

  // Calculate additional metrics
  const additionalMetrics = useMemo(() => {
    if (!program || !enhancedProgress) return null

    return {
      daysRemaining: getDaysRemaining(program),
      missedWorkouts: getMissedWorkouts(program),
      workoutsPerWeek: getWorkoutsPerWeek(program),
      completionDate: calculateCompletionDate(program, enhancedProgress),
    }
  }, [program, enhancedProgress])

  // Enhanced stats with total workouts from program
  const enhancedStats = useMemo(() => {
    if (!stats || !program) return stats

    return {
      ...stats,
      totalWorkouts: program.totalWorkouts,
    } as ProgramStats
  }, [stats, program])

  // Loading states
  const isLoading = programLoading || progressLoading || statsLoading
  const isRefreshing =
    programRefreshing || progressRefreshing || statsRefreshing
  const hasInitialData =
    hasInitialProgram || hasInitialProgress || hasInitialStats

  // Error handling
  const error = programError || progressError || statsError
  const hasPartialDataError = !programError && !!(progressError || statsError)

  const refetchAll = async () => {
    await Promise.all([refetchProgram(), refetchProgress(), refetchStats()])
  }

  return {
    program,
    progress: enhancedProgress,
    stats: enhancedStats,
    additionalMetrics,
    isLoading,
    isRefreshing,
    hasInitialData,
    error,
    refetch: refetchAll,
    hasPartialDataError,
    // Detailed loading states for granular control
    loadingStates: {
      programLoading,
      progressLoading,
      statsLoading,
      programRefreshing,
      progressRefreshing,
      statsRefreshing,
    },
  }
}
