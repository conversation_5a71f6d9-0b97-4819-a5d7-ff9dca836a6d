import { test, expect } from '@playwright/test'
import { authenticateUser } from './helpers'

test.describe('Navigation Height', () => {
  test.beforeEach(async ({ page }) => {
    await authenticateUser(page)
  })

  test('navigation bar should be 66px tall', async ({ page }) => {
    // Navigate to program page which uses the navigation
    await page.goto('/program')

    // Wait for navigation to be visible
    const navigationBar = page.locator('header').first()
    await expect(navigationBar).toBeVisible()

    // Get the bounding box to check actual height
    const boundingBox = await navigationBar.boundingBox()
    expect(boundingBox?.height).toBe(66)

    // Verify the inner container has correct height class
    const innerContainer = navigationBar.locator('.h-\\[66px\\]')
    await expect(innerContainer).toBeVisible()

    // Verify spacer div matches navigation height
    const spacer = page.locator('div.h-\\[66px\\]').last()
    await expect(spacer).toBeVisible()
  })

  test('navigation elements are properly centered in taller nav', async ({
    page,
  }) => {
    await page.goto('/program')

    const navigationBar = page.locator('header').first()
    const title = navigationBar.locator('h1')

    // Verify title is visible and centered
    await expect(title).toBeVisible()
    await expect(title).toContainText('Program')

    // Check vertical centering by comparing positions
    const navBox = await navigationBar.boundingBox()
    const titleBox = await title.boundingBox()

    if (navBox && titleBox) {
      const titleCenterY = titleBox.y + titleBox.height / 2
      const navCenterY = navBox.y + navBox.height / 2

      // Title should be vertically centered within 5px tolerance
      expect(Math.abs(titleCenterY - navCenterY)).toBeLessThanOrEqual(5)
    }
  })

  test('touch targets remain accessible in taller nav', async ({ page }) => {
    // Navigate to a page with back button
    await page.goto('/workout')
    await page.click('text=Start Workout')

    // Wait for exercise page with back button
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    const backButton = page.locator('button[aria-label="Go back"]')
    await expect(backButton).toBeVisible()

    // Verify back button has adequate touch target size
    const buttonBox = await backButton.boundingBox()
    expect(buttonBox?.width).toBeGreaterThanOrEqual(44)
    expect(buttonBox?.height).toBeGreaterThanOrEqual(44)
  })
})
