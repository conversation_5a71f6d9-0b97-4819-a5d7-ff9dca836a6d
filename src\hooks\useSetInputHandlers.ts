import { useState, useCallback } from 'react'
import { useSetInputValidation, ValidationError } from './useSetInputValidation'

interface SetInputHandlersProps {
  reps: number
  weight: number
  duration: number
  unit: 'lbs' | 'kg'
  isBodyweight?: boolean
  isTimeBased?: boolean
  onChange: (data: {
    reps?: number
    weight?: number
    duration?: number
  }) => void
}

export function useSetInputHandlers({
  reps,
  weight,
  duration,
  unit,
  isBodyweight = false,
  isTimeBased = false,
  onChange,
}: SetInputHandlersProps) {
  const [errors, setErrors] = useState<ValidationError>({})
  const { validateReps, validateWeight, validateDuration } =
    useSetInputValidation({
      isBodyweight,
    })

  const handleRepsChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseInt(value, 10) : value
      if (Number.isNaN(numValue)) return

      const error = validateReps(numValue)
      setErrors((prev) => ({ ...prev, reps: error }))
      onChange({ reps: numValue, weight })
    },
    [onChange, validateReps, weight]
  )

  const handleWeightChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseFloat(value) : value
      if (Number.isNaN(numValue)) return

      const error = validateWeight(numValue)
      setErrors((prev) => ({ ...prev, weight: error }))
      if (isTimeBased) {
        onChange({ weight: numValue, duration })
      } else {
        onChange({ reps, weight: numValue })
      }
    },
    [onChange, validateWeight, reps, isTimeBased, duration]
  )

  const handleDurationChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseInt(value, 10) : value
      if (Number.isNaN(numValue)) return

      const error = validateDuration(numValue)
      setErrors((prev) => ({ ...prev, duration: error }))
      onChange({ weight, duration: numValue })
    },
    [onChange, validateDuration, weight]
  )

  const incrementWeight = useCallback(() => {
    const increment = unit === 'kg' ? 2.5 : 5
    const newWeight = Math.min(weight + increment, 1000)
    handleWeightChange(newWeight)
  }, [weight, unit, handleWeightChange])

  const decrementWeight = useCallback(() => {
    const increment = unit === 'kg' ? 2.5 : 5
    const newWeight = Math.max(weight - increment, 0)
    handleWeightChange(newWeight)
  }, [weight, unit, handleWeightChange])

  const incrementReps = useCallback(() => {
    if (isTimeBased) return
    const newReps = Math.min(reps + 1, 100)
    handleRepsChange(newReps)
  }, [reps, isTimeBased, handleRepsChange])

  const decrementReps = useCallback(() => {
    if (isTimeBased) return
    const newReps = Math.max(reps - 1, 1)
    handleRepsChange(newReps)
  }, [reps, isTimeBased, handleRepsChange])

  return {
    errors,
    handleRepsChange,
    handleWeightChange,
    handleDurationChange,
    incrementWeight,
    decrementWeight,
    incrementReps,
    decrementReps,
  }
}
