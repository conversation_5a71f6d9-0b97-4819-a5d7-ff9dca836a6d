import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import DesignSystemPage from '../page'

// Mock the design system imports
vi.mock('@/design-system', () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
  useTheme: () => ({
    theme: 'subtle-depth',
    setTheme: vi.fn(),
  }),
}))

describe('Theme Selector Buttons', () => {
  it('should have consistent rounded-theme class on all theme buttons', () => {
    render(<DesignSystemPage />)

    const buttons = [
      screen.getByText('Subtle Depth'),
      screen.getByText('Flat Bold'),
      screen.getByText('Glassmorphism'),
      screen.getByText('Ultra-Minimal'),
    ]

    buttons.forEach((button) => {
      expect(button.className).toContain('rounded-theme')
    })
  })

  it('should apply active styles to selected theme button', () => {
    render(<DesignSystemPage />)

    const subtleDepthButton = screen.getByText('Subtle Depth')
    expect(subtleDepthButton.className).toContain('bg-brand-primary')
    expect(subtleDepthButton.className).toContain('text-text-inverse')

    const flatBoldButton = screen.getByText('Flat Bold')
    expect(flatBoldButton.className).toContain('bg-bg-tertiary')
    expect(flatBoldButton.className).toContain('text-text-primary')
  })
})
