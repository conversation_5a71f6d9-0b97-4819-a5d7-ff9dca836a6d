import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, expect, it, vi } from 'vitest'
import { RepsInput } from '../RepsInput'

describe('RepsInput', () => {
  const defaultProps = {
    reps: 10,
    onChange: vi.fn(),
    onIncrement: vi.fn(),
    onDecrement: vi.fn(),
  }

  it('renders with theme-aware text colors for label', () => {
    render(<RepsInput {...defaultProps} />)

    const label = screen.getByText('Reps')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('renders input with theme-aware text and border colors', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('text-text-primary')
    expect(input).toHaveClass('border-text-tertiary')
    expect(input).toHaveClass('bg-bg-secondary')
  })

  it('renders quick buttons with theme-aware colors', () => {
    render(<RepsInput {...defaultProps} />)

    const quickButtons = screen.getAllByRole('button')
    quickButtons.forEach((button) => {
      if (button.textContent === '10') {
        // Selected button
        expect(button).toHaveClass('bg-brand-primary')
        expect(button).toHaveClass('text-text-inverse')
        expect(button).toHaveClass('border-brand-primary')
      } else {
        // Unselected buttons
        expect(button).toHaveClass('bg-bg-secondary')
        expect(button).toHaveClass('text-text-primary')
        expect(button).toHaveClass('border-text-tertiary')
      }
    })
  })

  it('maintains focus styles with theme colors', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('focus:ring-brand-primary')
  })

  it('shows error state with semantic error color', () => {
    render(<RepsInput {...defaultProps} error="Invalid reps" />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    const errorText = screen.getByRole('alert')

    expect(input).toHaveClass('border-error')
    expect(errorText).toHaveClass('text-error')
  })

  it('shows disabled state with theme-aware colors', () => {
    render(<RepsInput {...defaultProps} disabled />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    const quickButtons = screen.getAllByRole('button')

    expect(input).toHaveClass('bg-bg-tertiary')
    expect(input).toHaveClass('text-text-tertiary')

    quickButtons.forEach((button) => {
      expect(button).toHaveClass('text-text-tertiary')
    })
  })

  describe('increment/decrement buttons', () => {
    it('renders increment and decrement buttons', () => {
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      expect(decrementBtn).toBeInTheDocument()
      expect(incrementBtn).toBeInTheDocument()
    })

    it('calls onIncrement when increment button is clicked', async () => {
      const user = userEvent.setup()
      render(<RepsInput {...defaultProps} />)

      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })
      await user.click(incrementBtn)

      expect(defaultProps.onIncrement).toHaveBeenCalledTimes(1)
    })

    it('calls onDecrement when decrement button is clicked', async () => {
      const user = userEvent.setup()
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      await user.click(decrementBtn)

      expect(defaultProps.onDecrement).toHaveBeenCalledTimes(1)
    })

    it('disables increment/decrement buttons when input is disabled', () => {
      render(<RepsInput {...defaultProps} disabled />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      expect(decrementBtn).toBeDisabled()
      expect(incrementBtn).toBeDisabled()
    })

    it('increment/decrement buttons have proper styling', () => {
      render(<RepsInput {...defaultProps} />)

      const decrementBtn = screen.getByRole('button', { name: 'Decrease reps' })
      const incrementBtn = screen.getByRole('button', { name: 'Increase reps' })

      // Check enabled state styling
      expect(decrementBtn).toHaveClass('bg-bg-secondary')
      expect(decrementBtn).toHaveClass('text-text-primary')
      expect(decrementBtn).toHaveClass('border-text-tertiary')
      expect(decrementBtn).toHaveClass('hover:bg-bg-tertiary')

      expect(incrementBtn).toHaveClass('bg-bg-secondary')
      expect(incrementBtn).toHaveClass('text-text-primary')
      expect(incrementBtn).toHaveClass('border-text-tertiary')
      expect(incrementBtn).toHaveClass('hover:bg-bg-tertiary')
    })

    it('increment/decrement buttons are not included in quick button count', () => {
      render(<RepsInput {...defaultProps} />)

      // Should only get quick buttons, not increment/decrement buttons
      const quickButtons = screen.getAllByRole('button').filter((btn) => {
        const label = btn.getAttribute('aria-label')
        return !label?.includes('crease')
      })

      expect(quickButtons).toHaveLength(5) // Default quick buttons
    })
  })
})
