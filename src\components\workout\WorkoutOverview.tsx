'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useMemo } from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { ExerciseCard } from '@/components/workout/ExerciseCard'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import {
  WorkoutCardSkeleton,
  ExerciseItemSkeleton,
} from '@/components/ui/Skeletons'
import { Badge } from '@/components/ui/Badge'
import { FloatingCTAButton } from '@/components/ui'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'

export function WorkoutOverview() {
  const router = useRouter()
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout,
    userProgramInfo,
    exercises,
    exerciseWorkSetsModels,
    expectedExerciseCount,
    hasInitialData,
    isLoadingFresh,
    isOffline,
    refreshWorkout,
    updateExerciseWorkSets,
    workoutSession,
    finishWorkout,
    isLoading,
    loadExerciseRecommendation,
  } = useWorkout()

  // Debug component lifecycle
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[WorkoutOverview] Component mounted')

      return () => {
        // eslint-disable-next-line no-console
        console.log('[WorkoutOverview] Component unmounting!')
      }
    }
    return () => {} // Return empty cleanup for production
  }, [])

  // Mark when workout page becomes interactive
  useEffect(() => {
    if (!isLoadingWorkout && todaysWorkout) {
      PerformanceMonitor.mark(PerformanceMarks.WORKOUT_PAGE_INTERACTIVE)

      // Report metrics in development
      if (process.env.NODE_ENV === 'development') {
        PerformanceMonitor.reportKeyMetrics()
      }
    }
  }, [isLoadingWorkout, todaysWorkout])

  // Pull-to-refresh functionality
  const pullToRefresh = usePullToRefresh({
    onRefresh: refreshWorkout,
    threshold: 80,
    enabled: !isLoadingWorkout && hasInitialData,
  })

  // Check if we have valid workout data structure
  const workoutGroup = todaysWorkout?.[0]
  const workout = workoutGroup?.WorkoutTemplates?.[0]

  // Extract exercises from workout data for display when workout hasn't started
  const previewExercises = useMemo(() => {
    if (!workout?.Exercises || workoutSession) {
      return []
    }
    // Create preview models from workout exercises
    return workout.Exercises.map((exercise) => ({
      Id: exercise.Id,
      Label: exercise.Label,
      BodyPartId: exercise.BodyPartId || 0,
      IsFinished: false,
      IsNextExercise: false,
      isLoadingSets: false,
      setsError: null,
      lastSetsUpdate: 0,
      sets: [], // Empty sets for preview
      IsBodyweight: exercise.IsBodyweight,
      WorkoutSets: [], // For compatibility with ExerciseCard
      isLoading: false,
      error: null,
    }))
  }, [workout, workoutSession])

  // Use exerciseWorkSetsModels when workout is active, otherwise use preview
  const displayExercises = workoutSession
    ? exerciseWorkSetsModels
    : previewExercises

  const handleStartWorkout = () => {
    if (todaysWorkout) {
      startWorkout(todaysWorkout)
      const firstExercise = exercises?.[0]
      if (firstExercise) {
        router.push(`/workout/exercise/${firstExercise.Id}`)
      }
    }
  }

  const handleFinishWorkout = async () => {
    try {
      await finishWorkout()
      router.push('/workout/complete')
    } catch (error) {
      // Handle error silently
    }
  }

  const hasCompletedSets =
    workoutSession?.exercises?.some(
      (exercise) => exercise.sets && exercise.sets.length > 0
    ) || false

  const getButtonLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue Workout'
    return 'Start Workout'
  }

  const getButtonAriaLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue your current workout'
    return 'Start a new workout session'
  }

  const handleExerciseClick = async (exerciseId: number) => {
    try {
      // Start workout if not already started
      if (todaysWorkout && !workoutSession) {
        await startWorkout(todaysWorkout)
      }

      // Pre-load the exercise recommendation before navigation
      const exercise = exercises?.find((ex) => ex.Id === exerciseId)
      if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
        // Show loading state
        updateExerciseWorkSets(exerciseId, [])

        try {
          // Try to load recommendation before navigation
          await loadExerciseRecommendation(exerciseId)
        } catch (error) {
          // Continue with navigation even if recommendation fails
          // The exercise page will handle the retry
          // Only log in development
          if (process.env.NODE_ENV === 'development') {
            console.warn(
              'Failed to pre-load recommendation, continuing to exercise page:',
              error
            )
          }
        }
      }

      // Navigate to exercise page
      router.push(`/workout/exercise/${exerciseId}`)
    } catch (error) {
      console.error('Error handling exercise click:', error)
      // Show error toast or notification
      // For now, still navigate to let the exercise page handle the error
      router.push(`/workout/exercise/${exerciseId}`)
    }
  }

  const handleRetryExercise = (exerciseId: number) => {
    // Retry loading sets for a specific exercise
    updateExerciseWorkSets(exerciseId, [])
  }

  // Show skeleton only when no data available at all
  if (!hasInitialData && isLoadingWorkout) {
    return (
      <div className="h-full bg-bg-primary">
        <div className="h-full overflow-y-auto">
          <div className="p-4 pb-24">
            <div className="mx-auto max-w-lg">
              {/* Title skeleton */}
              <div
                data-testid="workout-title-skeleton"
                className="mb-6 h-8 w-48 bg-bg-tertiary rounded animate-pulse"
              />

              {/* Workout info skeleton */}
              <div className="mb-6">
                <WorkoutCardSkeleton />
              </div>

              {/* Exercise list skeletons */}
              <div className="mb-8 space-y-3">
                {Array.from({ length: 5 }, (_, i) => (
                  <ExerciseItemSkeleton key={i} />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Floating Action Button (disabled while loading) */}
        <div className="fixed bottom-6 left-0 right-0 z-50">
          <div className="mx-auto max-w-lg w-full px-4">
            <button
              disabled
              className="w-full rounded-full bg-bg-tertiary py-4 text-lg font-semibold text-text-tertiary cursor-not-allowed min-h-[56px] shadow-theme-xl"
              data-testid="start-workout-button"
            >
              Loading...
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (workoutError) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-error">
            Error loading workout
          </h2>
          <p className="mb-4 text-text-secondary">
            {(() => {
              if (typeof workoutError === 'string') return workoutError
              if (workoutError instanceof Error) return workoutError.message
              return 'Failed to load workout'
            })()}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-theme bg-brand-primary px-6 py-3 text-text-inverse hover:bg-brand-primary/90 shadow-theme-md hover:shadow-theme-lg transition-all"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // No workout state - ensure we always render something valid
  if (
    !isLoading &&
    !isLoadingWorkout &&
    (!todaysWorkout || todaysWorkout.length === 0 || !workout)
  ) {
    // Log when showing no workout state
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[WorkoutOverview] Showing "No Workout Available" state', {
        todaysWorkout,
        isLoadingWorkout,
        hasInitialData,
        userProgramInfo,
      })
    }
    return (
      <div className="flex h-full items-center justify-center p-4">
        <div className="text-center max-w-md">
          <h2 className="mb-4 text-2xl font-semibold text-text-primary">
            No Workout Available
          </h2>
          <div className="space-y-3 text-text-secondary">
            <p>It looks like you don't have a workout program assigned yet.</p>
            <p className="text-sm">This could happen if:</p>
            <ul className="text-sm text-left list-disc list-inside space-y-1">
              <li>You're a new user and haven't been assigned a program</li>
              <li>Your current program has ended</li>
              <li>There's a sync issue with your account</li>
            </ul>
          </div>

          <div className="mt-6 space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full rounded-theme bg-brand-primary px-6 py-3 text-text-inverse hover:bg-brand-primary/90 shadow-theme-md hover:shadow-theme-lg transition-all"
            >
              Refresh Page
            </button>

            <p className="text-sm text-text-tertiary">
              If this continues, please contact support or check the Dr. Muscle
              mobile app.
            </p>
          </div>

          {/* Show debug info only in development */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-6">
              <summary className="cursor-pointer text-sm text-text-tertiary hover:text-text-secondary">
                Show Debug Info
              </summary>
              <div className="mt-2 p-4 bg-bg-secondary rounded-theme text-left text-xs border border-brand-primary/10">
                <div className="space-y-2">
                  <div>
                    <p className="font-semibold">userProgramInfo:</p>
                    <pre className="whitespace-pre-wrap overflow-auto max-h-40">
                      {userProgramInfo
                        ? JSON.stringify(userProgramInfo, null, 2)
                        : 'null'}
                    </pre>
                  </div>
                  <div>
                    <p className="font-semibold">todaysWorkout:</p>
                    <pre className="whitespace-pre-wrap overflow-auto max-h-40">
                      {todaysWorkout
                        ? JSON.stringify(todaysWorkout, null, 2)
                        : 'null'}
                    </pre>
                  </div>
                  <div>
                    <p className="font-semibold">Loading states:</p>
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(
                        { isLoadingWorkout, hasInitialData, isLoadingFresh },
                        null,
                        2
                      )}
                    </pre>
                  </div>
                </div>
              </div>
            </details>
          )}
        </div>
      </div>
    )
  }

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('[WorkoutOverview] Workout data check:', {
      hasTodaysWorkout: !!todaysWorkout,
      todaysWorkoutLength: todaysWorkout?.length,
      workoutGroup,
      hasWorkoutTemplates: !!workoutGroup?.WorkoutTemplates,
      workoutTemplatesLength: workoutGroup?.WorkoutTemplates?.length,
      workout,
    })
  }

  return (
    <div
      className="h-full flex flex-col bg-bg-primary relative"
      data-testid="workout-overview-container"
    >
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullToRefresh.pullDistance}
        threshold={80}
        isRefreshing={pullToRefresh.isRefreshing}
        isPulling={pullToRefresh.isPulling}
      />
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 pb-24">
          {/* Extra bottom padding for fixed button */}
          <div className="mx-auto max-w-lg">
            {/* Status indicators */}
            {(isOffline || pullToRefresh.isRefreshing) && (
              <div className="mb-4 text-center">
                {pullToRefresh.isRefreshing && (
                  <Badge variant="primary" size="sm">
                    Refreshing workout...
                  </Badge>
                )}
                {isOffline && (
                  <Badge variant="warning" size="sm">
                    Offline Mode
                  </Badge>
                )}
              </div>
            )}

            {/* Exercise List */}
            <div className="mb-8 space-y-3">
              {/* Show loaded exercises using ExerciseCard */}
              {displayExercises?.map((exercise) => (
                <ExerciseCard
                  key={exercise.Id}
                  exercise={exercise}
                  onExerciseClick={handleExerciseClick}
                  onRetry={handleRetryExercise}
                />
              ))}

              {/* Show skeleton loaders for remaining exercises if still loading */}
              {isLoadingWorkout &&
              expectedExerciseCount &&
              displayExercises &&
              expectedExerciseCount > displayExercises.length
                ? Array.from({
                    length: expectedExerciseCount - displayExercises.length,
                  }).map((_, index) => (
                    <ExerciseItemSkeleton
                      key={`skeleton-${displayExercises.length + index}`}
                    />
                  ))
                : null}
            </div>
          </div>
        </div>
      </div>

      {/* Floating CTA Button */}
      <FloatingCTAButton
        onClick={
          workoutSession && hasCompletedSets
            ? handleFinishWorkout
            : handleStartWorkout
        }
        label={getButtonLabel()}
        ariaLabel={getButtonAriaLabel()}
      />
    </div>
  )
}
