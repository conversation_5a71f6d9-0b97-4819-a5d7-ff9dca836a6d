/**
 * Progressive Loading Performance Monitor
 * Tracks and reports on the complete progressive loading flow
 */

import { userInfoPerformance } from './userInfoPerformance'
import { logger } from './logger'
import {
  DEFAULT_THRESHOLDS,
  type PerformanceThresholds,
} from './progressiveLoadingMonitor/constants'
import { generatePerformanceReport } from './progressiveLoadingMonitor/reportGenerator'
import {
  checkPerformanceViolations,
  sendPerformanceAlert,
} from './progressiveLoadingMonitor/performanceChecker'

export class ProgressiveLoadingMonitor {
  private static instance: ProgressiveLoadingMonitor

  private thresholds: PerformanceThresholds

  private monitoringStarted = false

  private alertsSent = new Set<string>()

  private constructor(thresholds = DEFAULT_THRESHOLDS) {
    this.thresholds = thresholds
  }

  static getInstance(
    thresholds?: PerformanceThresholds
  ): ProgressiveLoadingMonitor {
    if (!ProgressiveLoadingMonitor.instance) {
      ProgressiveLoadingMonitor.instance = new ProgressiveLoadingMonitor(
        thresholds
      )
    }
    return ProgressiveLoadingMonitor.instance
  }

  /**
   * Start monitoring a new session
   */
  startMonitoring(): void {
    this.monitoringStarted = true
    this.alertsSent.clear()

    if (process.env.NODE_ENV === 'development') {
      logger.log('[Progressive Loading Monitor] Started monitoring session')
    }
  }

  /**
   * Check performance against thresholds and report issues
   */
  checkPerformance(): void {
    if (!this.monitoringStarted) return

    const violations = checkPerformanceViolations(this.thresholds)

    // Report violations
    if (violations.length > 0 && process.env.NODE_ENV === 'development') {
      logger.warn('[Progressive Loading Monitor] Performance issues detected:')
      violations.forEach((violation) => logger.warn(`  - ${violation}`))
    }

    // Send alerts for production
    if (violations.length > 0 && process.env.NODE_ENV === 'production') {
      const metrics = userInfoPerformance.getMetrics()
      sendPerformanceAlert(violations, metrics, this.alertsSent)
    }
  }

  /**
   * Generate a performance report
   */
  // eslint-disable-next-line class-methods-use-this
  generateReport(): string {
    return generatePerformanceReport()
  }

  /**
   * Reset the monitor for a new session
   */
  reset(): void {
    this.monitoringStarted = false
    this.alertsSent.clear()
    userInfoPerformance.reset()
  }
}

// Export singleton instance
export const progressiveLoadingMonitor = ProgressiveLoadingMonitor.getInstance()

// Convenience functions
export function startProgressiveLoadingMonitoring(): void {
  progressiveLoadingMonitor.startMonitoring()
}

export function checkProgressiveLoadingPerformance(): void {
  progressiveLoadingMonitor.checkPerformance()
}

export function getProgressiveLoadingReport(): string {
  return progressiveLoadingMonitor.generateReport()
}

export function resetProgressiveLoadingMonitor(): void {
  progressiveLoadingMonitor.reset()
}

// Auto-report disabled to reduce console noise
// Uncomment the following code to enable auto-reporting in development:
// if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
//   window.addEventListener('load', () => {
//     setTimeout(() => {
//       const report = getProgressiveLoadingReport()
//       logger.log(report)
//       checkProgressiveLoadingPerformance()
//     }, 5000) // Wait 5 seconds after load to ensure all metrics are collected
//   })
// }
