import React from 'react'

interface FloatingCTAButtonProps {
  onClick: () => void
  label?: string
  ariaLabel?: string
}

export function FloatingCTAButton({
  onClick,
  label = 'Open Workout',
  ariaLabel = 'Open Workout - Start your next workout session',
}: FloatingCTAButtonProps) {
  return (
    <div
      data-testid="floating-cta-container"
      className="fixed bottom-6 left-0 right-0 z-50"
      role="navigation"
      aria-label="Primary actions"
    >
      <div className="max-w-lg mx-auto w-full px-4">
        <button
          onClick={onClick}
          className="w-full px-7 py-5 min-h-[62px] bg-gradient-to-r from-brand-primary to-brand-secondary text-text-inverse font-semibold text-base uppercase tracking-wider rounded-theme hover:scale-[1.02] active:scale-[0.98] shadow-lg shadow-brand-primary/25 hover:shadow-xl hover:shadow-brand-primary/30 transition-all focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2 focus:ring-offset-bg-primary"
          aria-label={ariaLabel}
          data-testid="start-workout-button"
        >
          {label}
        </button>
      </div>
    </div>
  )
}
