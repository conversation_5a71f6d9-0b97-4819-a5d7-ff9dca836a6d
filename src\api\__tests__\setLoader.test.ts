import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { SetLoader } from '../setLoader'
import type { WorkoutLogSerieModel } from '@/types'
import { workoutApi } from '@/api/workouts'

// Mock the workouts API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getExerciseSets: vi.fn(),
  },
}))

describe('SetLoader', () => {
  let setLoader: SetLoader
  const mockGetExerciseSets = vi.mocked(workoutApi.getExerciseSets)

  beforeEach(() => {
    setLoader = new SetLoader()
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Single Exercise Loading', () => {
    it('should load sets for a single exercise successfully', async () => {
      const mockSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets.mockResolvedValueOnce(mockSets)

      const result = await setLoader.loadExerciseSets(1)

      expect(result).toEqual(mockSets)
      expect(mockGetExerciseSets).toHaveBeenCalledWith(1)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1)
    })

    it('should return cached sets within TTL', async () => {
      const mockSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 110.23, Kg: 50 },
          Reps: 8,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets.mockResolvedValueOnce(mockSets)

      // First call - should hit API
      const result1 = await setLoader.loadExerciseSets(1)
      expect(result1).toEqual(mockSets)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1)

      // Second call immediately - should return cached
      const result2 = await setLoader.loadExerciseSets(1)
      expect(result2).toEqual(mockSets)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1) // No additional call

      // Advance time but within TTL (4 minutes)
      vi.advanceTimersByTime(4 * 60 * 1000)

      // Third call - should still return cached
      const result3 = await setLoader.loadExerciseSets(1)
      expect(result3).toEqual(mockSets)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1)
    })

    it('should refetch after cache expires', async () => {
      const mockSets1: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]
      const mockSets2: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 110, Kg: 49.9 },
          Reps: 8,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets
        .mockResolvedValueOnce(mockSets1)
        .mockResolvedValueOnce(mockSets2)

      // First call
      const result1 = await setLoader.loadExerciseSets(1)
      expect(result1).toEqual(mockSets1)

      // Advance time past TTL (6 minutes)
      vi.advanceTimersByTime(6 * 60 * 1000)

      // Second call - should fetch new data
      const result2 = await setLoader.loadExerciseSets(1)
      expect(result2).toEqual(mockSets2)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(2)
    })
  })

  describe('Retry Logic', () => {
    it('should retry on failure with exponential backoff', async () => {
      const mockSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      // Fail twice, then succeed
      mockGetExerciseSets
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockSets)

      const promise = setLoader.loadExerciseSets(1)

      // First attempt fails immediately
      await vi.advanceTimersByTimeAsync(0)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1)

      // Second attempt after ~1s (with jitter)
      await vi.advanceTimersByTimeAsync(1200)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(2)

      // Third attempt after ~2s more (with jitter)
      await vi.advanceTimersByTimeAsync(2400)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(3)

      const result = await promise
      expect(result).toEqual(mockSets)
    })

    it('should fail after max retries', async () => {
      mockGetExerciseSets.mockRejectedValue(new Error('Persistent error'))

      const promise = setLoader.loadExerciseSets(1)

      // Advance through all retry attempts
      await vi.advanceTimersByTimeAsync(0) // Initial attempt
      await vi.advanceTimersByTimeAsync(1500) // First retry
      await vi.advanceTimersByTimeAsync(3000) // Second retry
      await vi.advanceTimersByTimeAsync(6000) // Third retry

      await expect(promise).rejects.toThrow('Persistent error')
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(4) // Initial + 3 retries
    })

    it('should apply jitter to prevent thundering herd', async () => {
      // Test that multiple simultaneous failures have different retry times
      mockGetExerciseSets.mockRejectedValue(new Error('Server overload'))

      // Start loading multiple exercises but don't await
      setLoader.loadExerciseSets(1).catch(() => {})
      setLoader.loadExerciseSets(2).catch(() => {})
      setLoader.loadExerciseSets(3).catch(() => {})

      // Advance time to trigger retries
      await vi.advanceTimersByTimeAsync(1500)

      // Check that not all retries happened at exactly the same time
      const callTimes = mockGetExerciseSets.mock.calls.length
      expect(callTimes).toBeGreaterThanOrEqual(3) // At least initial calls
      expect(callTimes).toBeLessThanOrEqual(6) // Not all retried yet due to jitter
    })
  })

  describe('Batch Loading', () => {
    it('should load sets for multiple exercises', async () => {
      const mockSets1: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]
      const mockSets2: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 2,
          Weight: { Lb: 110.23, Kg: 50 },
          Reps: 12,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]
      const mockSets3: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 3,
          Weight: { Lb: 0, Kg: 0 },
          Reps: 15,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets
        .mockResolvedValueOnce(mockSets1)
        .mockResolvedValueOnce(mockSets2)
        .mockResolvedValueOnce(mockSets3)

      const results = await setLoader.batchLoadExerciseSets([1, 2, 3])

      expect(results).toEqual({
        1: { success: true, sets: mockSets1, error: null },
        2: { success: true, sets: mockSets2, error: null },
        3: { success: true, sets: mockSets3, error: null },
      })
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(3)
    })

    it('should handle mixed success/failure in batch', async () => {
      const mockSets1: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      // For exercise 2, make it fail all retries immediately
      let exercise2CallCount = 0
      mockGetExerciseSets
        .mockResolvedValueOnce(mockSets1) // Exercise 1 succeeds
        .mockImplementation((exerciseId) => {
          if (exerciseId === 2) {
            exercise2CallCount++
            return Promise.reject(new Error('Server error for exercise 2'))
          }
          return Promise.resolve([]) // Exercise 3 returns empty
        })

      const promise = setLoader.batchLoadExerciseSets([1, 2, 3])

      // Advance through all retries for exercise 2
      await vi.advanceTimersByTimeAsync(0) // Initial attempts
      await vi.advanceTimersByTimeAsync(1500) // First retry
      await vi.advanceTimersByTimeAsync(3000) // Second retry
      await vi.advanceTimersByTimeAsync(6000) // Third retry

      const results = await promise

      expect(results[1]?.success).toBe(true)
      expect(results[1]?.sets).toEqual(mockSets1)

      expect(results[2]?.success).toBe(false)
      expect(results[2]?.error).toBe('Server error for exercise 2')
      expect(results[2]?.sets).toEqual([])

      expect(results[3]?.success).toBe(true)
      expect(results[3]?.sets).toEqual([])

      // Verify exercise 2 was retried 4 times (initial + 3 retries)
      expect(exercise2CallCount).toBe(4)
    }, 10000)

    it('should use cache for some exercises in batch', async () => {
      const mockSets1: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]
      const mockSets2: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 2,
          Weight: { Lb: 110.23, Kg: 50 },
          Reps: 12,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      // Pre-load exercise 1
      mockGetExerciseSets.mockResolvedValueOnce(mockSets1)
      await setLoader.loadExerciseSets(1)

      // Clear mock to track new calls
      mockGetExerciseSets.mockClear()
      mockGetExerciseSets.mockResolvedValueOnce(mockSets2)

      // Batch load should use cache for exercise 1
      const results = await setLoader.batchLoadExerciseSets([1, 2])

      expect(results[1]?.sets).toEqual(mockSets1)
      expect(results[2]?.sets).toEqual(mockSets2)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1) // Only exercise 2
      expect(mockGetExerciseSets).toHaveBeenCalledWith(2)
    })
  })

  describe('Partial Data Handling', () => {
    it('should return empty array for exercises with no sets', async () => {
      mockGetExerciseSets.mockResolvedValueOnce([])

      const result = await setLoader.loadExerciseSets(1)
      expect(result).toEqual([])
    })

    it('should handle null/undefined responses gracefully', async () => {
      mockGetExerciseSets.mockResolvedValueOnce(
        null as unknown as WorkoutLogSerieModel[]
      )

      const result = await setLoader.loadExerciseSets(1)
      expect(result).toEqual([])
    })
  })

  describe('Force Refresh', () => {
    it('should bypass cache when force refresh is true', async () => {
      const mockSets1: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]
      const mockSets2: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 110, Kg: 49.9 },
          Reps: 8,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets
        .mockResolvedValueOnce(mockSets1)
        .mockResolvedValueOnce(mockSets2)

      // First load
      const result1 = await setLoader.loadExerciseSets(1)
      expect(result1).toEqual(mockSets1)

      // Force refresh immediately
      const result2 = await setLoader.loadExerciseSets(1, true)
      expect(result2).toEqual(mockSets2)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(2)
    })
  })

  describe('Cache Management', () => {
    it('should clear cache for specific exercise', async () => {
      const mockSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets.mockResolvedValue(mockSets)

      // Load and cache
      await setLoader.loadExerciseSets(1)
      mockGetExerciseSets.mockClear()

      // Clear cache
      setLoader.clearCache(1)

      // Next load should hit API
      await setLoader.loadExerciseSets(1)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(1)
    })

    it('should clear all cache', async () => {
      const mockSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Lb: 100, Kg: 45.36 },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      mockGetExerciseSets.mockResolvedValue(mockSets)

      // Load multiple exercises
      await setLoader.loadExerciseSets(1)
      await setLoader.loadExerciseSets(2)
      mockGetExerciseSets.mockClear()

      // Clear all cache
      setLoader.clearAllCache()

      // Next loads should hit API
      await setLoader.loadExerciseSets(1)
      await setLoader.loadExerciseSets(2)
      expect(mockGetExerciseSets).toHaveBeenCalledTimes(2)
    })
  })
})
