'use client'

import React from 'react'
import { useTheme } from '@/design-system'

export function ThemePhilosophy() {
  const { theme } = useTheme()

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-heading font-semibold mb-4">
        Theme Philosophy
      </h2>
      <div className="p-6 bg-bg-secondary rounded-lg space-y-4">
        {theme === 'subtle-depth' && (
          <>
            <h3 className="text-xl font-heading font-medium text-brand-primary">
              Subtle Depth - Premium Sophistication
            </h3>
            <p className="text-text-secondary">
              Rich, layered interfaces with micro-shadows and gentle gradients
              creating visual hierarchy. Deep charcoal backgrounds with golden
              accents evoke luxury and refinement.
            </p>
          </>
        )}
        {theme === 'flat-bold' && (
          <>
            <h3 className="text-xl font-heading font-medium text-brand-primary">
              Flat Bold - Power & Precision
            </h3>
            <p className="text-text-secondary">
              High-impact color blocks with sharp edges and no gradients. Bold
              typography with maximum contrast. Electric green accents on pure
              black/white for no-nonsense, powerful interfaces.
            </p>
          </>
        )}
        {theme === 'glassmorphism' && (
          <>
            <h3 className="text-xl font-heading font-medium text-brand-primary">
              Glassmorphism - Future Tech
            </h3>
            <p className="text-text-secondary">
              Frosted glass effects with layered transparency and aurora-like
              gradient meshes. Glowing accents and smooth transitions create a
              cutting-edge, futuristic aesthetic.
            </p>
          </>
        )}
        {theme === 'ultra-minimal' && (
          <>
            <h3 className="text-xl font-heading font-medium text-brand-primary">
              Ultra-Minimal - Pure Focus
            </h3>
            <p className="text-text-secondary">
              Maximum negative space as a luxury element with hairline borders
              and typography-driven hierarchy. Single accent color with
              black/white base where the interface disappears and content
              dominates.
            </p>
          </>
        )}
      </div>
    </section>
  )
}
