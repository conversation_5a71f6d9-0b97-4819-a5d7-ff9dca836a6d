import React from 'react'
import { render, screen } from '@testing-library/react'
import { Button } from '../Button'

describe('Button', () => {
  it('should render with default styles', () => {
    render(<Button>Click me</Button>)
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-brand-primary')
  })

  it('should apply gold gradient when goldGradient prop is true', () => {
    render(<Button goldGradient>Click me</Button>)
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).toHaveClass('shimmer-hover')
    expect(button).toHaveClass('text-shadow-sm')
    expect(button).not.toHaveClass('bg-brand-primary')
  })

  it('should not apply gold gradient when goldGradient prop is false', () => {
    render(<Button goldGradient={false}>Click me</Button>)
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toHaveClass('bg-brand-primary')
    expect(button).not.toHaveClass('bg-gradient-metallic-gold')
  })

  it('should work with other variants when goldGradient is applied', () => {
    render(
      <Button variant="secondary" goldGradient>
        Click me
      </Button>
    )
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).not.toHaveClass('bg-bg-secondary')
  })
})
