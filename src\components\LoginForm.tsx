'use client'

import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useAuth } from '@/hooks/useAuth'
import { useOAuth } from '@/hooks/useOAuth'
import { useRouter, useSearchParams } from 'next/navigation'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'
import { useHapticFeedback } from '@/hooks/useHapticFeedback'
import { sanitizeEmail, sanitizeInput } from '@/utils/sanitization'
import { Button } from '@/components/ui'
import type { LoginFormData } from '@/types'

interface LoginFormProps {
  onSuccess?: () => void
}

export function LoginForm({ onSuccess }: LoginFormProps = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, isLoading, error } = useAuth()
  const oauth = useOAuth()
  const haptic = useHapticFeedback()
  const [showPassword, setShowPassword] = useState(false)
  const [socialError, setSocialError] = useState<string | null>(null)

  const returnUrl = searchParams.get('from') || '/workout'

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setFocus,
  } = useForm<LoginFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
    },
  })

  // Auto-focus email input on mount
  useEffect(() => {
    setFocus('email')
  }, [setFocus])

  // Clear social error after timeout
  useEffect(() => {
    if (socialError) {
      const timer = setTimeout(() => setSocialError(null), 5000)
      return () => clearTimeout(timer)
    }
    // Return cleanup function that does nothing when socialError is null
    return () => {}
  }, [socialError])

  const onSubmit = async (data: LoginFormData) => {
    // Mark login start for performance tracking
    PerformanceMonitor.mark(PerformanceMarks.LOGIN_START)

    // Haptic feedback for mobile
    haptic.light()

    try {
      // Sanitize inputs before sending to API
      const sanitizedEmail = sanitizeEmail(data.email)
      const sanitizedPassword = sanitizeInput(data.password)

      await login({
        Username: sanitizedEmail,
        Password: sanitizedPassword,
      })

      // Mark login success
      PerformanceMonitor.mark(PerformanceMarks.LOGIN_SUCCESS)

      // Success haptic feedback
      haptic.success()

      // Small delay to ensure auth state is persisted
      await new Promise((resolve) => {
        setTimeout(resolve, 100)
      })

      // Call onSuccess callback if provided, otherwise redirect
      if (onSuccess) {
        onSuccess()
      } else {
        router.push(returnUrl)
      }
    } catch (err) {
      // Error haptic feedback
      haptic.error()
      // Error is handled by useAuth hook
    }
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-6 animate-slide-up"
      data-testid="login-form"
    >
      {/* API Error Display */}
      {(error || socialError) && (
        <div
          role="alert"
          className="bg-red-500/10 border border-red-500/20 text-red-500 px-4 py-3 rounded-theme"
        >
          <p className="text-sm">{error || socialError}</p>
        </div>
      )}

      {/* Email Input */}
      <div>
        <label
          htmlFor="email"
          className="block text-sm sm:text-base font-medium text-text-secondary mb-2"
        >
          Email
        </label>
        <input
          id="email"
          type="email"
          inputMode="email"
          autoComplete="email"
          autoCapitalize="none"
          autoCorrect="off"
          aria-required="true"
          aria-invalid={!!errors.email}
          aria-describedby={errors.email ? 'email-error' : undefined}
          className={`w-full h-12 sm:h-14 px-4 border rounded-theme text-base sm:text-lg transition-all duration-200 bg-bg-tertiary text-text-primary ${
            errors.email ? 'border-red-500' : 'border-brand-primary/20'
          } focus:outline-none focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary`}
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Please enter a valid email',
            },
          })}
        />
        {errors.email && (
          <p
            id="email-error"
            role="alert"
            className="mt-2 text-sm text-red-500"
          >
            {errors.email.message}
          </p>
        )}
      </div>

      {/* Password Input */}
      <div>
        <label
          htmlFor="password"
          className="block text-sm sm:text-base font-medium text-text-secondary mb-2"
        >
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            autoCapitalize="none"
            autoCorrect="off"
            aria-required="true"
            aria-invalid={!!errors.password}
            aria-describedby={errors.password ? 'password-error' : undefined}
            className={`w-full h-12 sm:h-14 px-4 pr-12 border rounded-theme text-base sm:text-lg transition-all duration-200 bg-bg-tertiary text-text-primary ${
              errors.password ? 'border-red-500' : 'border-brand-primary/20'
            } focus:outline-none focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary`}
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            })}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-text-tertiary hover:text-text-secondary transition-colors"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                />
              </svg>
            ) : (
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            )}
          </button>
        </div>
        {errors.password && (
          <p
            id="password-error"
            role="alert"
            className="mt-2 text-sm text-red-500"
          >
            {errors.password.message}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={!isValid || isLoading}
        loading={isLoading}
        fullWidth
        size="lg"
        variant="primary"
        goldGradient
        aria-label={isLoading ? 'Logging in' : 'Login'}
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>

      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-brand-primary/20" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-bg-secondary text-text-tertiary">
            Or continue with
          </span>
        </div>
      </div>

      {/* OAuth Buttons */}
      <div className="space-y-3">
        {/* Google Sign In */}
        <button
          type="button"
          onClick={async () => {
            // Haptic feedback
            haptic.light()

            // Clear any previous errors
            setSocialError(null)

            // Perform OAuth sign in
            await oauth.signInWithGoogle(
              () => {
                // Success callback
                haptic.success()
                if (onSuccess) {
                  onSuccess()
                } else {
                  router.push(returnUrl)
                }
              },
              (error) => {
                // Error callback
                haptic.error()
                setSocialError(error.message)
              }
            )
          }}
          disabled={isLoading || oauth.isLoading === 'google'}
          className={`w-full h-12 px-4 border rounded-theme font-medium flex items-center justify-center transition-all duration-200 ${
            isLoading || oauth.isLoading === 'google'
              ? 'border-brand-primary/20 bg-bg-tertiary text-text-tertiary cursor-not-allowed opacity-60'
              : 'border-brand-primary/20 bg-bg-secondary text-text-primary hover:bg-bg-tertiary hover:border-brand-primary/40 active:scale-95 tap-highlight-none touch-manipulation'
          }`}
        >
          <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {oauth.isLoading === 'google' ? (
            <div className="flex items-center">
              <div className="animate-spin h-5 w-5 mr-3 border-2 border-text-tertiary border-t-transparent rounded-full" />
              Signing in...
            </div>
          ) : (
            'Sign in with Google'
          )}
        </button>

        {/* Apple Sign In */}
        <button
          type="button"
          onClick={async () => {
            // Haptic feedback
            haptic.light()

            // Clear any previous errors
            setSocialError(null)

            // Perform OAuth sign in
            await oauth.signInWithApple(
              () => {
                // Success callback
                haptic.success()
                if (onSuccess) {
                  onSuccess()
                } else {
                  router.push(returnUrl)
                }
              },
              (error) => {
                // Error callback
                haptic.error()
                setSocialError(error.message)
              }
            )
          }}
          disabled={isLoading || oauth.isLoading === 'apple'}
          className={`w-full h-12 px-4 border rounded-theme font-medium flex items-center justify-center transition-all duration-200 ${
            isLoading || oauth.isLoading === 'apple'
              ? 'border-brand-primary/20 bg-bg-tertiary text-text-tertiary cursor-not-allowed opacity-60'
              : 'border-brand-primary/20 bg-text-primary text-bg-primary hover:bg-text-primary/90 active:scale-95 tap-highlight-none touch-manipulation'
          }`}
        >
          <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
          </svg>
          {oauth.isLoading === 'apple' ? (
            <div className="flex items-center">
              <div className="animate-spin h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full" />
              Signing in...
            </div>
          ) : (
            'Sign in with Apple'
          )}
        </button>
      </div>
    </form>
  )
}
