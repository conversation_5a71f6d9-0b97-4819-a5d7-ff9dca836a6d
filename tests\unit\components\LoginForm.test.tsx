import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { LoginForm } from '@/components/LoginForm'
import { useAuth } from '@/hooks/useAuth'
import { useRouter, useSearchParams } from 'next/navigation'

// Mock dependencies
vi.mock('@/hooks/useAuth')
vi.mock('next/navigation')

// Create wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('LoginForm Component', () => {
  const mockLogin = vi.fn()
  const mockPush = vi.fn()
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useAuth hook
    ;(useAuth as any).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: vi.fn(),
    })

    // Mock useRouter
    ;(useRouter as any).mockReturnValue({
      push: mockPush,
    })

    // Mock useSearchParams
    ;(useSearchParams as any).mockReturnValue({
      get: vi.fn().mockReturnValue(null), // Default to null, can be overridden in specific tests
    })

    // Mock haptic feedback
    global.navigator.vibrate = vi.fn()
  })

  describe('Mobile-First Rendering', () => {
    it('should render login form with mobile-optimized inputs', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      // Check for email input with mobile optimization
      const emailInput = screen.getByLabelText('Email')
      expect(emailInput).toBeInTheDocument()
      expect(emailInput).toHaveAttribute('type', 'email')
      expect(emailInput).toHaveAttribute('autocomplete', 'email')

      // Check for password input
      const passwordInput = screen.getByLabelText('Password')
      expect(passwordInput).toBeInTheDocument()
      expect(passwordInput).toHaveAttribute('type', 'password')

      // Check for submit button
      const submitButton = screen.getByRole('button', { name: /login/i })
      expect(submitButton).toBeInTheDocument()
    })

    it('should have touch-friendly input sizes', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Check minimum height for touch targets (44px)
      expect(emailInput).toHaveClass('h-12') // 48px in Tailwind
      expect(passwordInput).toHaveClass('h-12')
      expect(submitButton).toHaveClass('h-14') // 56px in Tailwind
    })

    it('should apply gold gradient to login button', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const submitButton = screen.getByRole('button', { name: /login/i })

      // Check for gold gradient classes
      expect(submitButton).toHaveClass('bg-gradient-metallic-gold')
      expect(submitButton).toHaveClass('shimmer-hover')
      expect(submitButton).toHaveClass('text-shadow-sm')
    })

    it('should display show/hide password toggle', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const passwordInput = screen.getByLabelText('Password')
      const toggleButton = screen.getByRole('button', {
        name: /show password/i,
      })

      expect(passwordInput).toHaveAttribute('type', 'password')

      // Click toggle to show password
      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'text')

      // Click again to hide password
      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'password')
    })
  })

  describe('Form Validation', () => {
    it('should show validation errors for invalid email', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter invalid email
      await user.type(emailInput, 'invalid-email')
      await user.click(submitButton)

      // Check for error message
      const errorMessage = await screen.findByText(
        /please enter a valid email/i
      )
      expect(errorMessage).toBeInTheDocument()
      expect(errorMessage).toHaveClass('text-red-500')
    })

    it('should show validation error for short password', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter valid email but short password
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'short')
      await user.click(submitButton)

      // Check for error message
      const errorMessage = await screen.findByText(
        /password must be at least 8 characters/i
      )
      expect(errorMessage).toBeInTheDocument()
    })

    it('should disable submit button when form is invalid', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const submitButton = screen.getByRole('button', { name: /login/i })

      // Initially disabled (empty form)
      expect(submitButton).toBeDisabled()

      // Type invalid email
      const emailInput = screen.getByLabelText('Email')
      await user.type(emailInput, 'invalid')

      // Should still be disabled
      expect(submitButton).toBeDisabled()
    })

    it('should enable submit button when form is valid', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter valid credentials
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')

      // Should be enabled
      expect(submitButton).toBeEnabled()
    })
  })

  describe('Form Submission', () => {
    it('should handle successful login', async () => {
      // Mock login to resolve successfully
      mockLogin.mockResolvedValueOnce(undefined)

      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter valid credentials
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')

      // Submit form
      await user.click(submitButton)

      // Check that login was called with correct credentials
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          Username: '<EMAIL>',
          Password: 'password123',
        })
      })

      // Check haptic feedback was triggered (note: it's called with a number, not array)
      expect(navigator.vibrate).toHaveBeenCalledWith(50)
    })

    it('should show loading state during login', async () => {
      // Mock loading state
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: true,
        error: null,
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      const submitButton = screen.getByRole('button', { name: /logging in/i })
      expect(submitButton).toBeDisabled()
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('should redirect on successful login', async () => {
      // Mock successful login
      mockLogin.mockResolvedValueOnce(undefined)

      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter credentials and submit
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Since login is async, wait for it to complete
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalled()
      })

      // Then check redirect - login is mocked to resolve immediately
      expect(mockPush).toHaveBeenCalledWith('/workout')
    })

    it('should redirect to return URL on successful login', async () => {
      // Mock successful login
      mockLogin.mockResolvedValueOnce(undefined)

      // Mock search params with return URL
      ;(useSearchParams as any).mockReturnValue({
        get: vi.fn().mockReturnValue('/workout/exercise/123'),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Enter credentials and submit
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Since login is async, wait for it to complete
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalled()
      })

      // Then check redirect to return URL
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should display API errors', async () => {
      // Mock auth hook with error
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: false,
        error: 'Invalid credentials',
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      // Error should be displayed
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
      expect(screen.getByRole('alert')).toHaveClass('bg-red-50')
    })
  })

  describe('Mobile Keyboard Optimization', () => {
    it('should auto-focus email input on mount', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      expect(document.activeElement).toBe(emailInput)
    })

    it('should have proper input attributes for mobile keyboards', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')

      // Email input optimizations
      expect(emailInput).toHaveAttribute('inputMode', 'email')
      expect(emailInput).toHaveAttribute('autoCapitalize', 'none')
      expect(emailInput).toHaveAttribute('autoCorrect', 'off')

      // Password input optimizations
      expect(passwordInput).toHaveAttribute('autoCapitalize', 'none')
      expect(passwordInput).toHaveAttribute('autoCorrect', 'off')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      expect(screen.getByLabelText('Email')).toHaveAttribute(
        'aria-required',
        'true'
      )
      expect(screen.getByLabelText('Password')).toHaveAttribute(
        'aria-required',
        'true'
      )
      expect(screen.getByRole('button', { name: /login/i })).toHaveAttribute(
        'aria-label'
      )
    })

    it('should announce errors to screen readers', async () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const emailInput = screen.getByLabelText('Email')
      const submitButton = screen.getByRole('button', { name: /login/i })

      // Trigger validation error
      await user.type(emailInput, 'invalid')
      await user.click(submitButton)

      const errorMessage = await screen.findByText(
        /please enter a valid email/i
      )
      expect(errorMessage).toHaveAttribute('role', 'alert')
    })
  })

  describe('Network Error Handling', () => {
    it('should display helpful network error messages', async () => {
      // Given: Mock auth hook with network error
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: false,
        error: 'Network error. Please check your connection and try again.',
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      // Then: Specific error message is shown
      expect(
        screen.getByText(
          /Network error. Please check your connection and try again/
        )
      ).toBeInTheDocument()
    })

    it('should display offline error message', async () => {
      // Given: Mock auth hook with offline error
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: false,
        error: 'No internet connection. Please check your network.',
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      // Then: Offline error message is shown
      expect(
        screen.getByText(/No internet connection. Please check your network/)
      ).toBeInTheDocument()
    })

    it('should display timeout error message', async () => {
      // Given: Mock auth hook with timeout error
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: false,
        error: 'Connection timeout. Please check your internet speed.',
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      // Then: Timeout error message is shown
      expect(
        screen.getByText(/Connection timeout. Please check your internet speed/)
      ).toBeInTheDocument()
    })
  })

  describe('OAuth Integration', () => {
    it('should render Google OAuth button', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const googleButton = screen.getByText('Continue with Google')
      expect(googleButton).toBeInTheDocument()
      expect(googleButton).toHaveClass('bg-white', 'text-gray-700')
    })

    it('should render Apple Sign In button', () => {
      render(<LoginForm />, { wrapper: createWrapper() })

      const appleButton = screen.getByText('Sign in with Apple')
      expect(appleButton).toBeInTheDocument()
      expect(appleButton).toHaveClass('bg-black', 'text-white')
    })

    it('should show coming soon alert for Google OAuth', async () => {
      // Mock alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      render(<LoginForm />, { wrapper: createWrapper() })

      const googleButton = screen.getByText('Continue with Google')
      await user.click(googleButton)

      expect(alertSpy).toHaveBeenCalledWith(
        'Google Sign In coming soon! Please use email login for now.'
      )

      alertSpy.mockRestore()
    })

    it('should show coming soon alert for Apple Sign In', async () => {
      // Mock alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      render(<LoginForm />, { wrapper: createWrapper() })

      const appleButton = screen.getByText('Sign in with Apple')
      await user.click(appleButton)

      expect(alertSpy).toHaveBeenCalledWith(
        'Apple Sign In coming soon! Please use email login for now.'
      )

      alertSpy.mockRestore()
    })

    it('should disable OAuth buttons when form is loading', async () => {
      // Given: Mock loading state
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isLoading: true,
        error: null,
        clearError: vi.fn(),
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      // Then: OAuth buttons are disabled
      expect(screen.getByText('Continue with Google')).toBeDisabled()
      expect(screen.getByText('Sign in with Apple')).toBeDisabled()
    })

    it('should provide haptic feedback on OAuth button click', async () => {
      // Mock navigator.vibrate
      const vibrateSpy = vi.fn()
      Object.defineProperty(navigator, 'vibrate', {
        value: vibrateSpy,
        writable: true,
      })

      render(<LoginForm />, { wrapper: createWrapper() })

      const googleButton = screen.getByText('Continue with Google')
      await user.click(googleButton)

      expect(vibrateSpy).toHaveBeenCalledWith(50)
    })
  })
})
