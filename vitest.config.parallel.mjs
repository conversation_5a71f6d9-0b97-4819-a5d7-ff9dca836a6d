import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { fileURLToPath } from 'url'
import os from 'os'

// Optimal worker configuration
const cpuCount = os.cpus().length
const optimalWorkers = Math.max(1, Math.floor(cpuCount * 0.75)) // Use 75% of CPUs

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'happy-dom',
    globals: true,
    setupFiles: './tests/setup.ts',
    css: false,
    
    // Parallel execution settings
    pool: 'threads', // Use worker threads for better performance
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
        maxThreads: optimalWorkers,
        minThreads: Math.max(1, Math.floor(optimalWorkers / 2)),
      }
    },
    
    // Test isolation for parallel safety
    isolate: true,
    
    // Faster reporter for parallel execution  
    reporters: process.env.CI ? ['verbose'] : [['default', { summary: false }]],
    
    // Disable coverage during regular test runs for speed
    coverage: {
      enabled: false,
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'text-summary'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/mockData/**',
        'src/types/**',
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80,
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})