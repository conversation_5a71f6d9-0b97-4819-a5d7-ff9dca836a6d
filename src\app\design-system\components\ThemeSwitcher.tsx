'use client'

import React from 'react'
import { useTheme } from '@/design-system'

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="flex flex-col sm:flex-row gap-2 p-4 bg-bg-secondary rounded-lg">
      <button
        onClick={() => setTheme('subtle-depth')}
        className={`px-4 py-2 rounded-theme ${
          theme === 'subtle-depth'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Subtle Depth
      </button>
      <button
        onClick={() => setTheme('flat-bold')}
        className={`px-4 py-2 rounded-theme ${
          theme === 'flat-bold'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Flat Bold
      </button>
      <button
        onClick={() => setTheme('glassmorphism')}
        className={`px-4 py-2 rounded-theme ${
          theme === 'glassmorphism'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Glassmorphism
      </button>
      <button
        onClick={() => setTheme('ultra-minimal')}
        className={`px-4 py-2 rounded-theme ${
          theme === 'ultra-minimal'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Ultra-Minimal
      </button>
    </div>
  )
}
