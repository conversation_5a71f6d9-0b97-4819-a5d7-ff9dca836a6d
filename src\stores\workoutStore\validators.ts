/**
 * Validation functions for the workout store
 */

import type { WorkoutTemplateModel, RecommendationModel } from '@/types'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout-types'
import { logger } from '@/utils/logger'

// Helper functions for validation
export const isValidUserProgramInfo = (
  data: unknown
): data is GetUserWorkoutProgramTimeZoneInfoResponse => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  // Check if GetUserProgramInfoResponseModel exists
  if (
    !('GetUserProgramInfoResponseModel' in obj) ||
    obj.GetUserProgramInfoResponseModel === null ||
    typeof obj.GetUserProgramInfoResponseModel !== 'object'
  ) {
    return false
  }

  const programInfo = obj.GetUserProgramInfoResponseModel as Record<
    string,
    unknown
  >

  // Check for RecommendedProgram
  if (
    !('RecommendedProgram' in programInfo) ||
    programInfo.RecommendedProgram === null ||
    typeof programInfo.RecommendedProgram !== 'object'
  ) {
    return false
  }

  const recommendedProgram = programInfo.RecommendedProgram as Record<
    string,
    unknown
  >

  return (
    'Id' in recommendedProgram &&
    'Label' in recommendedProgram &&
    typeof recommendedProgram.Id === 'number' &&
    typeof recommendedProgram.Label === 'string'
  )
}

export const isValidWorkoutTemplate = (
  data: unknown
): data is WorkoutTemplateModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  // More flexible validation - Id might be string or number
  const hasValidId =
    'Id' in obj && (typeof obj.Id === 'number' || typeof obj.Id === 'string')
  const hasValidLabel = 'Label' in obj && typeof obj.Label === 'string'
  // Accept both French (Exercices) and English (Exercises) field names
  const hasValidExercices =
    ('Exercices' in obj && Array.isArray(obj.Exercices)) ||
    ('Exercises' in obj && Array.isArray(obj.Exercises))

  const isValid = hasValidId && hasValidLabel && hasValidExercices

  if (!isValid) {
    // Only log detailed validation errors in development to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      logger.error('Workout template validation failed:', {
        hasValidId,
        hasValidLabel,
        hasValidExercices,
        id: obj.Id,
        idType: typeof obj.Id,
        label: obj.Label,
        labelType: typeof obj.Label,
        exercices: obj.Exercices || obj.Exercises,
        exercicesIsArray:
          Array.isArray(obj.Exercices) || Array.isArray(obj.Exercises),
        allKeys: Object.keys(obj),
      })
    } else {
      // In production, just log a simple warning
      logger.warn('Invalid workout template data received')
    }
  }

  return isValid
}

export const isValidWorkoutTemplateArray = (
  data: unknown
): data is WorkoutTemplateModel[] => {
  return Array.isArray(data) && data.every(isValidWorkoutTemplate)
}

export const isValidRecommendation = (
  data: unknown
): data is RecommendationModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  // Series and Reps are required
  if (!('Series' in obj) || !('Reps' in obj)) {
    return false
  }

  if (typeof obj.Series !== 'number' || typeof obj.Reps !== 'number') {
    return false
  }

  // Weight field must exist but can be null (for bodyweight exercises)
  if (!('Weight' in obj)) {
    return false
  }

  // If Weight is not null, validate its structure
  if (obj.Weight !== null) {
    if (typeof obj.Weight !== 'object') {
      return false
    }
    const weight = obj.Weight as Record<string, unknown>
    if (typeof weight.Lb !== 'number' || typeof weight.Kg !== 'number') {
      return false
    }
  }

  return true
}
