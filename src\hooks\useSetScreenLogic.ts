import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRIR } from '@/hooks/useRIR'
import { calculatePercentageChange, roundWeight } from '@/utils/weightUtils'
import type { RecommendationModel } from '@/types'

export function useSetScreenLogic(exerciseId?: number) {
  const router = useRouter()
  const { saveSet, isLoading, error, getRecommendation } = useWorkout()
  const {
    exercises,
    currentExerciseIndex,
    currentSetIndex,
    workoutSession,
    nextSet,
    setCurrentSetIndex,
    nextExercise,
    setCurrentExerciseById,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()
  const { mapRIRValueToNumber, saveRIR } = useRIR()

  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [showRIRPicker, setShowRIRPicker] = useState(false)
  const [showComplete, setShowComplete] = useState(false)
  const [showExerciseComplete, setShowExerciseComplete] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [showSetSaved, setShowSetSaved] = useState(false)
  const [recommendation, setRecommendation] =
    useState<RecommendationModel | null>(null)

  // Handle deep linking - find exercise by ID if provided
  useEffect(() => {
    if (exerciseId && exercises.length > 0) {
      const exerciseIndex = exercises.findIndex((ex) => ex.Id === exerciseId)
      if (exerciseIndex === -1) {
        // Invalid exercise ID, redirect to workout
        router.replace('/workout')
      } else {
        // Sync the store's currentExerciseIndex with the URL parameter
        setCurrentExerciseById(exerciseId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exerciseId]) // Only depend on exerciseId to prevent infinite re-renders

  // Current exercise and set info
  const currentExercise = exercises[currentExerciseIndex]
  const isWarmup = currentSetIndex < (recommendation?.WarmupsCount || 0)
  const isFirstWorkSet = currentSetIndex === (recommendation?.WarmupsCount || 0)
  const totalSets = recommendation?.Series || 4
  const isLastSet = currentSetIndex === totalSets - 1
  const isLastExercise = currentExerciseIndex === exercises.length - 1

  // Get completed sets for current exercise
  const completedSets =
    workoutSession?.exercises
      .find((ex) => ex.exerciseId === currentExercise?.Id)
      ?.sets.map((set) => ({
        Id: 0, // Temporary ID for completed sets
        ExerciseId: currentExercise?.Id || 0,
        Reps: set.reps,
        Weight: set.weight,
        RIR: set.rir,
        IsWarmups: set.isWarmup,
        SetNo: set.setNumber.toString(),
        IsFinished: true,
        IsNext: false,
      })) || []

  // Load recommendation when exercise changes
  useEffect(() => {
    if (currentExercise) {
      // First check cache
      const cached = getCachedExerciseRecommendation(currentExercise.Id)
      if (cached) {
        setRecommendation(cached)
      } else {
        // Load from API with proper error handling
        getRecommendation(currentExercise.Id)
          .then((rec) => {
            if (rec) {
              setRecommendation(rec)
            }
          })
          .catch((error) => {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(
                '[useSetScreenLogic] Failed to load recommendation:',
                error
              )
            }
          })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentExercise?.Id]) // Only depend on the exercise ID to prevent infinite re-renders

  // Set form data - don't default to 100 pounds for bodyweight exercises
  const [setData, setSetData] = useState({
    reps: recommendation?.Reps || 8,
    weight: recommendation?.Weight?.Lb
      ? roundWeight(recommendation.Weight.Lb)
      : 0,
    duration: currentExercise?.Timer || 45,
  })

  // Update form data when recommendation changes
  useEffect(() => {
    if (recommendation) {
      setSetData({
        reps: recommendation.Reps || 8,
        weight: recommendation.Weight?.Lb
          ? roundWeight(recommendation.Weight.Lb)
          : 0,
        duration: currentExercise?.Timer || 45,
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [recommendation, currentExercise?.Timer]) // Only depend on specific properties to prevent infinite re-renders

  // Calculate performance percentage
  const performancePercentage = useCallback(() => {
    if (!recommendation?.ReferenceSetHistory) return null
    const currentWeight = recommendation.Weight.Lb
    const previousWeight = recommendation.ReferenceSetHistory.Weight.Lb
    return calculatePercentageChange(currentWeight, previousWeight)
  }, [recommendation])

  // Handle progression after set save
  const handleProgressAfterSave = () => {
    if (isLastSet) {
      // Show exercise complete message
      setShowExerciseComplete(true)
      // After 2 seconds, move to next exercise or complete workout
      setTimeout(() => {
        if (isLastExercise) {
          setShowComplete(true)
        } else {
          nextExercise()
          setShowExerciseComplete(false)
          // Navigate to rest timer between exercises
          router.push('/workout/rest-timer')
        }
      }, 2000)
    } else {
      // Show set saved indicator
      setShowSetSaved(true)
      setIsTransitioning(true)

      // Wait a moment to show the saved state
      setTimeout(() => {
        // Move to next set
        nextSet()
        // Navigate to rest timer between sets
        router.push('/workout/rest-timer?between-sets=true')
      }, 600)
    }
  }

  // Handle set save
  const handleSaveSet = async () => {
    if (!currentExercise || !workoutSession) return

    setIsSaving(true)
    setSaveError(null)

    try {
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: currentExercise.IsTimeBased ? undefined : setData.reps,
        weight: setData.weight,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: currentExercise.IsTimeBased ? setData.duration : undefined,
        RIR: undefined, // RIR will be collected after first work set
      })

      // Check if we need to show RIR picker
      if (isFirstWorkSet && !currentExercise.IsTimeBased) {
        setShowRIRPicker(true)
      } else {
        // Progress to next set or exercise
        handleProgressAfterSave()
      }
    } catch (err: unknown) {
      setSaveError(err instanceof Error ? err.message : 'Failed to save set')

      // Preserve workout data on 401 errors
      if (err && typeof err === 'object' && 'response' in err) {
        const axiosError = err as { response?: { status?: number } }
        if (axiosError.response?.status === 401 && workoutSession) {
          const preservedData = {
            exerciseId: currentExercise.Id,
            setNumber: currentSetIndex + 1,
            weight: setData.weight,
            reps: setData.reps,
            timestamp: Date.now(),
            workoutSession,
          }
          localStorage.setItem(
            'preserved_workout',
            JSON.stringify(preservedData)
          )
        }
      }
    } finally {
      setIsSaving(false)
    }
  }

  // Handle RIR selection
  const handleRIRSelect = async (rirValue: string) => {
    try {
      const numericRIR = mapRIRValueToNumber(rirValue)

      // Navigate immediately to timer with RIR value
      // This prevents the exercise page from showing after RIR selection
      router.push(
        `/workout/rest-timer?between-sets=true&rir=${numericRIR}&shouldProgress=true`
      )

      // Close modal after navigation starts
      setShowRIRPicker(false)

      // Update the current set data with RIR
      const { updateCurrentSet } = useWorkoutStore.getState()
      updateCurrentSet({ rir: numericRIR })

      // Mark RIR as captured for this exercise
      await saveRIR()
    } catch (err) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to save RIR:', err)
      }
      setShowRIRPicker(false)
      handleProgressAfterSave()
    }
  }

  // Handle RIR cancel
  const handleRIRCancel = () => {
    setShowRIRPicker(false)
    handleProgressAfterSave()
  }

  // Handle set click (for multi-set display)
  const handleSetClick = (setIndex: number) => {
    // Update the current set index to the clicked set
    setCurrentSetIndex(setIndex)

    // Update form data based on the selected set
    if (recommendation) {
      const warmupCount = recommendation.WarmupsCount || 0
      const isWarmupSet = setIndex < warmupCount

      if (isWarmupSet) {
        // For warmup sets
        const warmupIndex = setIndex
        const warmupSet = recommendation.WarmUpsList?.[warmupIndex]
        setSetData({
          reps: warmupSet?.WarmUpReps || recommendation.WarmUpReps1 || 5,
          weight:
            warmupSet?.WarmUpWeightSet?.Lb ||
            recommendation.WarmUpWeightSet1?.Lb ||
            0,
          duration: currentExercise?.Timer || 45,
        })
      } else {
        // For work sets
        setSetData({
          reps: recommendation.Reps || 8,
          weight: recommendation.Weight?.Lb || 0,
          duration: currentExercise?.Timer || 45,
        })
      }
    }
  }

  // Refetch recommendation
  const refetchRecommendation = useCallback(async () => {
    if (currentExercise) {
      const fresh = await getRecommendation(currentExercise.Id)
      setRecommendation(fresh)
    }
  }, [currentExercise, getRecommendation])

  return {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    isWarmup,
    isFirstWorkSet,
    totalSets,
    isLastSet,
    isLastExercise,
    currentSetIndex,
    setData,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    showSetSaved,
    recommendation,
    isLoading,
    error,
    completedSets,

    // Actions
    setSetData,
    setSaveError,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
    performancePercentage,
    handleSetClick,
  }
}
