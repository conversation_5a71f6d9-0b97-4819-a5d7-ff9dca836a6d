import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useProgramWithCalculationsAndCache } from '../useProgramWithCalculationsAndCache'

// Mock the dependent hooks
vi.mock('../useProgramWithCache', () => ({
  useProgramWithCache: vi.fn(() => ({
    program: { id: 1, name: 'Test Program', totalWorkouts: 10 },
    isLoading: false,
    isRefreshing: false,
    error: null,
    refetch: vi.fn(),
    hasInitialData: true,
  })),
  useProgramProgressWithCache: vi.fn(() => ({
    progress: { percentage: 50 },
    isLoading: false,
    isRefreshing: false,
    error: null,
    refetch: vi.fn(),
    hasInitialData: true,
  })),
  useProgramStatsWithCache: vi.fn(() => ({
    stats: { weekStreak: 2, workoutsCompleted: 5, lbsLifted: 1000 },
    isLoading: false,
    isRefreshing: false,
    error: null,
    refetch: vi.fn(),
    hasInitialData: true,
  })),
}))

// Mock program calculations
vi.mock('@/lib/programCalculations', () => ({
  calculateProgress: vi.fn(() => ({
    percentage: 50,
    daysCompleted: 10,
    currentWeek: 2,
    totalWorkouts: 10,
    workoutsThisWeek: 3,
    remainingWorkouts: 5,
  })),
  calculateCompletionDate: vi.fn(() => new Date()),
  getDaysRemaining: vi.fn(() => 20),
  getMissedWorkouts: vi.fn(() => 0),
  getWorkoutsPerWeek: vi.fn(() => 3),
}))

describe('useProgramWithCalculationsAndCache', () => {
  let consoleLogSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    // Spy on console.log
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  it('should not log to console in production', () => {
    // Set NODE_ENV to production
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'

    // Render the hook
    renderHook(() => useProgramWithCalculationsAndCache())

    // Verify console.log was not called
    expect(consoleLogSpy).not.toHaveBeenCalled()

    // Restore NODE_ENV
    process.env.NODE_ENV = originalEnv
  })

  it('should not log stats data in development', () => {
    // Set NODE_ENV to development
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    // Render the hook
    renderHook(() => useProgramWithCalculationsAndCache())

    // Check if console.log was called with stats data
    const statsLogCall = consoleLogSpy.mock.calls.find(
      (call) => call[0] === '[useProgramWithCalculationsAndCache] Stats data:'
    )

    // After fix, this should not be called
    expect(statsLogCall).toBeUndefined()

    // Restore NODE_ENV
    process.env.NODE_ENV = originalEnv
  })

  it('should return combined data from all hooks', () => {
    const { result } = renderHook(() => useProgramWithCalculationsAndCache())

    expect(result.current.program).toBeDefined()
    expect(result.current.progress).toBeDefined()
    expect(result.current.stats).toBeDefined()
    expect(result.current.isLoading).toBe(false)
  })
})
