import { test, expect } from '@playwright/test'
// eslint-disable-next-line import/extensions
import { login } from './utils/auth'

test.describe('Floating Buttons Consistency', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should have consistent floating button styles across pages', async ({
    page,
  }) => {
    // Test Program page floating button
    await page.goto('/program')
    await page.waitForSelector('[data-testid="floating-cta-container"]')

    const programButton = page.locator('[data-testid="floating-cta-container"]')
    await expect(programButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50 px-4/
    )

    const programInnerButton = programButton.locator('button')
    await expect(programInnerButton).toHaveClass(/rounded-full/)
    await expect(programInnerButton).toHaveClass(/shadow-theme-xl/)
    await expect(programInnerButton).toHaveClass(/min-h-\[56px\]/)

    // Test Workout Overview page floating button
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="floating-cta-container"]')

    const workoutButton = page.locator('[data-testid="floating-cta-container"]')
    await expect(workoutButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50 px-4/
    )

    const workoutInnerButton = workoutButton.locator('button')
    await expect(workoutInnerButton).toHaveClass(/rounded-full/)
    await expect(workoutInnerButton).toHaveClass(/shadow-theme-xl/)
    await expect(workoutInnerButton).toHaveClass(/min-h-\[56px\]/)

    // Test Exercise/SetScreen floating save button
    await workoutInnerButton.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="floating-save-button"]')

    const saveButton = page.locator('[data-testid="floating-save-button"]')
    await expect(saveButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50 px-4/
    )

    const saveInnerButton = saveButton.locator('button')
    await expect(saveInnerButton).toHaveClass(/rounded-full/)
    await expect(saveInnerButton).toHaveClass(/shadow-theme-xl/)
    await expect(saveInnerButton).toHaveClass(/min-h-\[56px\]/)

    // Fill in set data
    await page.fill('input[id="reps-input"]', '10')
    await page.fill('input[id="weight-input"]', '100')

    // Save set to trigger rest timer
    await saveInnerButton.click()

    // Wait for rest timer page
    await page.waitForURL('/workout/rest-timer')
    await page.waitForSelector('[data-testid="floating-skip-button"]')

    // Test Rest Timer floating skip button
    const skipButton = page.locator('[data-testid="floating-skip-button"]')
    await expect(skipButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50 px-4/
    )

    const skipInnerButton = skipButton.locator('button')
    await expect(skipInnerButton).toHaveClass(/rounded-full/)
    await expect(skipInnerButton).toHaveClass(/shadow-theme-xl/)
    await expect(skipInnerButton).toHaveClass(/min-h-\[56px\]/)
  })

  test('floating buttons should be visible on mobile viewport', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Test on Program page
    await page.goto('/program')
    await page.waitForSelector('[data-testid="floating-cta-container"]')

    const programButton = page.locator(
      '[data-testid="floating-cta-container"] button'
    )
    await expect(programButton).toBeVisible()
    await expect(programButton).toBeInViewport()

    // Test on Workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="floating-cta-container"]')

    const workoutButton = page.locator(
      '[data-testid="floating-cta-container"] button'
    )
    await expect(workoutButton).toBeVisible()
    await expect(workoutButton).toBeInViewport()
  })

  test('floating buttons should not overlap content', async ({ page }) => {
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="floating-cta-container"]')

    // Check that content has bottom padding to prevent overlap
    const content = page.locator('.pb-24')
    await expect(content).toBeVisible()

    // Start workout and check exercise page
    const startButton = page.locator(
      '[data-testid="floating-cta-container"] button'
    )
    await startButton.click()

    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForSelector('[data-testid="floating-save-button"]')

    // Check that content has bottom padding on exercise page too
    const exerciseContent = page.locator('.pb-24')
    await expect(exerciseContent).toBeVisible()
  })
})
