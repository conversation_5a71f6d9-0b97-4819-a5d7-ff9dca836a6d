import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/stores/authStore'
import { workoutApi } from '@/api/workouts'
import { calculateRecoveryInfo } from '@/utils/recoveryCalculations'
import type { ProgramType, RecoveryInfo } from '@/utils/recoveryCalculations'

interface UseWelcomeCardReturn {
  recoveryInfo: RecoveryInfo | null
  coachMessage: string | null
  programName: string | null
  nextWorkoutName: string | null
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

function determineProgramType(programName?: string): ProgramType {
  if (!programName) return 'other'

  const lowerName = programName.toLowerCase()

  if (
    lowerName.includes('split') ||
    lowerName.includes('push') ||
    lowerName.includes('pull') ||
    lowerName.includes('legs')
  ) {
    return 'split'
  }

  if (lowerName.includes('full') || lowerName.includes('body')) {
    return 'full-body'
  }

  if (lowerName.includes('power') || lowerName.includes('lifting')) {
    return 'powerlifting'
  }

  return 'other'
}

export function useWelcomeCard(): UseWelcomeCardReturn {
  const user = useAuthStore((state) => state.user)

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['welcomeCard', user?.email],
    queryFn: async () => {
      const programInfo = await workoutApi.getUserProgramInfo()

      // API response received

      // Check multiple possible sources for last workout date
      let lastWorkoutDateValue =
        programInfo?.LastWorkoutDate || programInfo?.LastWorkoutDateStr

      // If LastWorkoutDate is null, check SetsDate array
      if (
        !lastWorkoutDateValue &&
        programInfo?.SetsDate &&
        Array.isArray(programInfo.SetsDate)
      ) {
        // SetsDate is an array of dates, get the most recent (first) one
        if (programInfo.SetsDate.length > 0 && programInfo.SetsDate[0]) {
          ;[lastWorkoutDateValue] = programInfo.SetsDate
        }
      }

      if (!lastWorkoutDateValue) {
        return null
      }

      // Parse the last workout date
      const lastWorkoutDate = new Date(lastWorkoutDateValue)

      // Parse the workout date

      // Check if date is valid
      if (Number.isNaN(lastWorkoutDate.getTime())) {
        throw new Error('Invalid last workout date')
      }

      // Get user age from profile if available (to be implemented later)
      const userAge = undefined // TODO: Get from user profile API

      // Determine program type from name
      const programType = determineProgramType(
        programInfo?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label
      )

      // Get consecutive workout count (for future use)
      // let consecutiveWorkouts = 0
      // if (typeof programInfo.ConsecutiveWeeks === 'number') {
      //   consecutiveWorkouts = programInfo.ConsecutiveWeeks
      // } else if (programInfo.LastConsecutiveWorkoutDays) {
      //   consecutiveWorkouts = programInfo.LastConsecutiveWorkoutDays
      // }

      // Calculate recovery info
      const recoveryInfo = calculateRecoveryInfo(
        lastWorkoutDate,
        programType,
        userAge
      )

      // Recovery calculation completed

      // The coach message is already included in recoveryInfo.message
      const coachMessage = recoveryInfo.message

      return {
        recoveryInfo,
        coachMessage,
        programName:
          programInfo?.GetUserProgramInfoResponseModel?.RecommendedProgram
            ?.Label || null,
        nextWorkoutName:
          programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate
            ?.Label || null,
      }
    },
    enabled: !!user,
    staleTime: 60 * 1000, // Consider data stale after 1 minute
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
    retry: 2,
  })

  return {
    recoveryInfo: data?.recoveryInfo || null,
    coachMessage: data?.coachMessage || null,
    programName: data?.programName || null,
    nextWorkoutName: data?.nextWorkoutName || null,
    isLoading,
    error: error as Error | null,
    refetch,
  }
}
