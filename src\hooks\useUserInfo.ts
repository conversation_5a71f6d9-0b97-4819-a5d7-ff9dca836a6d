import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/stores/authStore'
import { userProfileApi } from '@/api/userProfile'
import { useEffect, useRef } from 'react'
import {
  startSession,
  endSession,
  trackUserInfoFetch,
  trackUserInfoComplete,
} from '@/utils/userInfoPerformance'
import { shouldClearCacheOnError } from '@/utils/apiRetry'
import {
  isCorruptedCacheError,
  clearCorruptedCache,
} from '@/utils/errorHandling'

interface UseUserInfoResult {
  isLoading: boolean
  error: Error | null
  refetch: () => Promise<unknown>
  hasUserInfo: boolean
  isCacheHit: boolean
  isBackgroundLoading: boolean
}

export function useUserInfo(): UseUserInfoResult {
  const {
    user,
    updateUser,
    isAuthenticated,
    getCachedUserInfo,
    setCachedUserInfo,
    isCacheStale,
    clearUserInfoCache,
  } = useAuthStore()

  // Track if component is mounted for background loading
  const isMountedRef = useRef(true)
  const sessionIdRef = useRef<string | undefined>(undefined)

  // Check cache on mount (but don't use it for display)
  let cachedData = null
  let hasCachedData = false
  let cacheIsStale = false

  try {
    cachedData = getCachedUserInfo()
    hasCachedData = Boolean(cachedData)
    cacheIsStale = isCacheStale()
  } catch (error) {
    // Handle corrupted cache
    if (isCorruptedCacheError(error)) {
      // Corrupted user info cache detected, clearing...
      clearCorruptedCache('auth')
    }
  }

  // Check if user info is missing - just check firstName since that's what we display
  const needsUserInfo = Boolean(isAuthenticated && user && !user.firstName)

  // Start new session if needed
  useEffect(() => {
    if (needsUserInfo && !sessionIdRef.current) {
      sessionIdRef.current = startSession()
    }
  }, [needsUserInfo])

  // Track component mount status
  useEffect(() => {
    isMountedRef.current = true
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const { data, isLoading, error, refetch, isFetching, dataUpdatedAt } =
    useQuery({
      queryKey: ['userInfo', user?.email],
      queryFn: async () => {
        if (!needsUserInfo) {
          return null
        }

        // Record cache status
        if (hasCachedData && !cacheIsStale) {
          trackUserInfoComplete(true) // fromCache = true
        } else {
          trackUserInfoFetch()
        }

        try {
          // API call with retry logic is already handled in authApi
          const userInfo = await userProfileApi.getUserInfo()
          return userInfo
        } catch (error) {
          // Don't retry if it's a 404 - endpoint doesn't exist
          if ((error as Error).message === 'UserInfo endpoint not available') {
            // UserInfo API not available, skipping user info fetch
            return null
          }

          // Check if we should clear cache on this error
          if (shouldClearCacheOnError(error)) {
            clearUserInfoCache()
          }

          // Failed to fetch user info
          throw error
        }
      },
      enabled: needsUserInfo,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: false, // Retry is handled by apiRetry utility
      // Always fetch fresh data, even if we have cache
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    })

  // Update auth store and cache when user info is fetched
  useEffect(() => {
    if (data && dataUpdatedAt > 0) {
      // Handle both wrapped (StatusCode/Result) and direct response formats
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const userInfo = (data as any).Result || data

      if (userInfo.Firstname || userInfo.Lastname) {
        // Map API response to User interface
        // Note: API uses lowercase 'n' in field names
        const userData = {
          firstName: userInfo.Firstname,
          lastName: userInfo.Lastname,
        }

        // Update user in auth store
        updateUser(userData)

        // Update cache with full user info
        setCachedUserInfo({
          firstName: userInfo.Firstname,
          lastName: userInfo.Lastname,
          ...userInfo, // Include any additional fields
        })

        // End session tracking on success
        if (sessionIdRef.current) {
          endSession(sessionIdRef.current, true)
          sessionIdRef.current = undefined
        }
      }
    }
  }, [data, dataUpdatedAt, updateUser, setCachedUserInfo])

  // Track errors
  useEffect(() => {
    if (error && sessionIdRef.current) {
      endSession(sessionIdRef.current, false)
      sessionIdRef.current = undefined
    }
  }, [error])

  // Determine if we're showing cached data
  const isCacheHit = hasCachedData && !cacheIsStale && !data

  // Check if we're loading in the background (have cached data but fetching fresh)
  const isBackgroundLoading = hasCachedData && isFetching

  return {
    isLoading: needsUserInfo && isLoading && !hasCachedData,
    error: needsUserInfo ? error : null,
    refetch,
    hasUserInfo: !needsUserInfo || !!data || hasCachedData,
    isCacheHit,
    isBackgroundLoading,
  }
}
