'use client'

import { useEffect, useState, useCallback, useMemo, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { SetScreen } from '@/components/workout/SetScreen'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { SetScreenProgressiveLoading } from '@/components/workout/SetScreenProgressiveLoading'
import { GoldLoadingTransition } from '@/components/workout/GoldLoadingTransition'
import { CrossFadeTransition } from '@/components/ui/FadeTransition'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { ExerciseModel } from '@/types'

// Debug counter to track component renders and API calls
// Reset counters when component is unmounted to avoid stale counts
let renderCount = 0
let apiCallCount = 0

interface ExercisePageClientProps {
  exerciseId: number
}

export function ExercisePageClient({ exerciseId }: ExercisePageClientProps) {
  // Increment render count on each render
  renderCount++

  // Log render with controlled frequency
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && renderCount % 10 === 1) {
      // eslint-disable-next-line no-console
      console.log(
        `🔄 [DEBUG] ExercisePageClient rendered ${renderCount} times, API calls: ${apiCallCount}`
      )
    }
  }) // No dependency array to run on every render

  const router = useRouter()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)
  const [showGoldTransition, setShowGoldTransition] = useState(true)
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout,
    exercises,
    workoutSession,
    loadRecommendation,
    updateExerciseWorkSets,
  } = useWorkout()
  const {
    setCurrentExerciseById,
    loadingStates,
    getCachedExerciseRecommendation,
    clearLoadingState,
  } = useWorkoutStore()

  // Hide gold transition after 400ms
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowGoldTransition(false)
    }, 400)

    return () => clearTimeout(timer)
  }, [])

  // Add timeout for stuck loading states
  useEffect(() => {
    if (!exerciseId) return

    const isLoadingRecommendation = loadingStates.get(exerciseId)
    if (!isLoadingRecommendation) return

    // If loading for more than 45 seconds, consider it stuck and clear the loading state
    const timeoutId = setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(
          `[ExercisePageClient] Clearing stuck loading state for exercise ${exerciseId}`
        )
      }
      // Clear the stuck loading state without triggering a reload
      clearLoadingState(exerciseId)
    }, 45000) // 45 second timeout

    return () => clearTimeout(timeoutId)
  }, [exerciseId, loadingStates, clearLoadingState])

  // Track errors in console and prevent spam
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const originalError = console.error
      let errorCount = 0
      const errorCache = new Map<string, number>()

      console.error = (...args) => {
        // Create a key from the first argument (usually the error message)
        const errorKey = String(args[0])
        const cachedCount = errorCache.get(errorKey) || 0

        // Only log the error if we haven't seen it too many times
        if (cachedCount < 3) {
          errorCache.set(errorKey, cachedCount + 1)
          originalError.apply(console, args)
        }

        errorCount++
        if (errorCount % 50 === 1 && errorCount > 50) {
          // eslint-disable-next-line no-console
          console.warn(
            `⚠️ [DEBUG] Suppressing repeated console errors: ${errorCount} total errors detected`
          )
        }
      }

      return () => {
        console.error = originalError
        // Reset counters on unmount
        renderCount = 0
        apiCallCount = 0
      }
    }
    // Return undefined for production
    return undefined
  }, [])

  // Log render info (only on exerciseId change to reduce spam)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('🏋️ [ExercisePageClient] Component mounted for exercise', {
        exerciseId,
        hasWorkoutSession: !!workoutSession,
        hasTodaysWorkout: !!todaysWorkout,
        isLoadingWorkout,
        exercisesCount: exercises?.length || 0,
      })
    }
  }, [
    exerciseId,
    exercises?.length,
    isLoadingWorkout,
    todaysWorkout,
    workoutSession,
  ]) // Include all dependencies

  // Retry function for manual initialization
  const retryInitialization = async () => {
    try {
      setIsInitializing(true)
      setLoadingError(null)

      // Start workout if needed
      if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
        const workoutGroup = todaysWorkout[0]
        const workout = workoutGroup?.WorkoutTemplates?.[0]

        if (workout) {
          await startWorkout(todaysWorkout)
        } else {
          router.replace('/workout')
          return
        }
      }

      // Set current exercise and load recommendations
      if (exerciseId) {
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          if (exercise) {
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        const exercise = exercises?.find((ex) => ex.Id === exerciseId)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          updateExerciseWorkSets(exerciseId, [])
        }
      }
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to retry initialization:', error)
      }
      setLoadingError(
        error instanceof Error
          ? error
          : new Error('Failed to retry initialization')
      )
    } finally {
      setIsInitializing(false)
    }
  }

  // Split initialization into separate effects to avoid dependency issues

  // Effect 1: Start workout if needed
  useEffect(() => {
    let isMounted = true

    async function startWorkoutIfNeeded() {
      try {
        setLoadingError(null)

        if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
          // Only log in development
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('🚀 [ExercisePageClient] Starting workout...', {
              todaysWorkout,
            })
          }

          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            await startWorkout(todaysWorkout)

            // Check if component is still mounted before updating state
            if (!isMounted) return

            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.log(
                '✅ [ExercisePageClient] Workout started successfully'
              )
            }
          } else {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.log(
                '❌ [ExercisePageClient] No workout template found, redirecting...'
              )
            }

            if (isMounted) {
              router.replace('/workout')
            }
          }
        }
      } catch (error) {
        // Check if component is still mounted before updating state
        if (!isMounted) return

        // Only log in development
        if (
          process.env.NODE_ENV === 'development' &&
          error instanceof Error &&
          !error.message.includes('401')
        ) {
          console.error('Failed to start workout:', error.message)
        }
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to start workout')
        )
      }
    }

    startWorkoutIfNeeded()

    return () => {
      isMounted = false
    }
  }, [workoutSession, todaysWorkout, isLoadingWorkout, startWorkout, router])

  // Effect 2: Set current exercise (only once per exerciseId)
  useEffect(() => {
    if (!exerciseId) return

    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('🎯 [ExercisePageClient] Setting current exercise', {
        exerciseId,
      })
    }

    // Set current exercise by ID
    setCurrentExerciseById(exerciseId)
    setIsInitializing(false)
  }, [exerciseId, setCurrentExerciseById])

  // Effect 3: Load recommendation (only when needed)
  // Use a ref to track if we've already initiated loading for this exercise
  const loadingInitiatedRef = useRef(new Set<number>())

  useEffect(() => {
    if (!exerciseId || !exercises || exercises.length === 0) return

    // Check if we've already initiated loading for this exercise
    if (loadingInitiatedRef.current.has(exerciseId)) return

    const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
    const isLoadingRecommendation = loadingStates.get(exerciseId)

    // Only load if we don't have a recommendation and aren't already loading
    if (!hasRecommendation && !isLoadingRecommendation) {
      const exercise = exercises.find((ex) => ex.Id === exerciseId)

      if (exercise) {
        // Mark as loading initiated
        loadingInitiatedRef.current.add(exerciseId)

        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log(
            '📡 [ExercisePageClient] Loading recommendation for exercise',
            {
              exerciseId,
              exerciseLabel: exercise.Label,
            }
          )
        }

        // Track API call
        apiCallCount++
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log(`📊 [DEBUG] API call #${apiCallCount} for recommendation`)
        }

        loadRecommendation(exerciseId, exercise.Label || 'Exercise')
      }
    }
  }, [
    exerciseId,
    exercises,
    getCachedExerciseRecommendation,
    loadingStates,
    loadRecommendation,
  ])

  // Effect 4: Update exercise work sets (only when needed)
  useEffect(() => {
    if (!exerciseId || !exercises || exercises.length === 0) return

    const exercise = exercises.find((ex) => ex.Id === exerciseId)
    if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log(
          '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
        )
      }
      updateExerciseWorkSets(exerciseId, [])
    }
  }, [exerciseId, exercises, updateExerciseWorkSets])

  // All hooks must be called before any conditional returns
  const isLoadingRecommendation = useMemo(
    () => (exerciseId ? loadingStates.get(exerciseId) : false),
    [exerciseId, loadingStates]
  )

  // Determine the current loading state for smooth transitions
  const getLoadingState = useCallback(() => {
    if (showGoldTransition) return 'gold-transition'
    if (isInitializing || isLoadingWorkout) return 'initializing'
    if (isLoadingRecommendation) return 'loading-recommendation'
    return 'ready'
  }, [
    showGoldTransition,
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
  ])

  const loadingState = getLoadingState()
  const currentExercise = exercises?.find((ex) => ex.Id === exerciseId)
  const exerciseName = currentExercise?.Label
  const cachedRecommendation = getCachedExerciseRecommendation(exerciseId)

  // Log render decision only when loading state changes significantly
  const prevLoadingStateRef = useRef<string>('')
  useEffect(() => {
    const currentState = getLoadingState()
    if (
      process.env.NODE_ENV === 'development' &&
      currentState !== prevLoadingStateRef.current
    ) {
      // eslint-disable-next-line no-console
      console.log('🎨 [ExercisePageClient] Loading state changed', {
        from: prevLoadingStateRef.current,
        to: currentState,
        hasError: !!loadingError || !!workoutError,
      })
      prevLoadingStateRef.current = currentState
    }
  }, [loadingState, loadingError, workoutError, getLoadingState])

  // Show error state with retry option
  if (loadingError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
        <p className="text-red-600 mb-4">
          Failed to load exercise: {loadingError.message}
        </p>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2"
          onClick={retryInitialization}
        >
          Retry
        </button>
        <button
          className="px-4 py-2 bg-gray-600 text-white rounded-lg"
          onClick={() => router.push('/workout')}
        >
          Back to Workout
        </button>
      </div>
    )
  }

  // Handle workout errors
  if (workoutError) {
    return (
      <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
        <div className="text-center p-6 max-w-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            Failed to Load Workout
          </h2>
          <p className="text-gray-600 mb-6">
            {typeof workoutError === 'string'
              ? workoutError
              : workoutError.message || 'Unable to load workout data.'}
          </p>
          <button
            onClick={() => router.push('/workout')}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
          >
            Back to Workout
          </button>
        </div>
      </div>
    )
  }

  return (
    <CrossFadeTransition transitionKey={loadingState} duration={200}>
      {loadingState === 'gold-transition' && (
        <GoldLoadingTransition exerciseName={exerciseName} />
      )}

      {loadingState === 'initializing' && (
        <SetScreenLoadingState
          exerciseName={exerciseName}
          showDetailedSkeleton={!!exercises && exercises.length > 0}
        />
      )}

      {loadingState === 'loading-recommendation' && currentExercise && (
        <SetScreenProgressiveLoading
          currentExercise={
            {
              ...currentExercise,
              IsSystemExercise: false,
              IsSwapTarget: false,
              IsUnilateral: false,
              IsTimeBased: false,
              VideoUrl: '',
              IsEasy: false,
              IsFlexibility: false,
              IsWeighted: true,
              SetStyle: 'Normal',
              IsPlate: false,
              IsPyramid: false,
              IsNormalSets: true,
              IsBodypartPriority: false,
              IsOneHanded: false,
            } as ExerciseModel
          } // Type assertion for missing properties
          recommendation={cachedRecommendation}
          isWarmup={false} // TODO: Determine warmup status
          currentSetIndex={0} // TODO: Get actual set index
          totalSets={cachedRecommendation?.Series || 3} // TODO: Get actual total sets
          showSetSaved={false}
          performancePercentage={() => null} // TODO: Calculate performance
          isLoadingRecommendation
        />
      )}

      {loadingState === 'ready' && <SetScreen exerciseId={exerciseId} />}
    </CrossFadeTransition>
  )
}
